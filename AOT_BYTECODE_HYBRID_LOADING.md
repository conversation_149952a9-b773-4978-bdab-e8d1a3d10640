# AOT + Bytecode 混合加载解决方案

## 问题描述

在 AOT 运行时上加载 bytecode 数据时，会遇到 `library 'dart:async' is already loaded` 错误。这是因为原始的 bytecode 加载器假设所有库都是新的，不允许重复加载。

## 解决方案

我们参考 `isolate_reload.cc` 的增量更新机制，实现了一个混合加载系统，能够：

1. **检测已存在的库**: 区分新库和已存在的库
2. **增量更新**: 只更新变化的部分，保持现有状态
3. **安全合并**: 避免破坏 AOT 中已有的数据结构

## 核心修改

### 1. 增量库声明读取

```cpp
void BytecodeReaderHelper::ReadLibraryDeclarations(intptr_t num_libraries) {
  // 分离新库和已存在的库
  auto& existing_libraries = GrowableObjectArray::Handle(Z, GrowableObjectArray::New());
  auto& new_libraries = GrowableObjectArray::Handle(Z, GrowableObjectArray::New());
  
  // 检查每个库是否已存在
  for (intptr_t i = 0; i < num_libraries; ++i) {
    uri ^= ReadObject();
    library = Library::LookupLibrary(thread_, uri);
    if (!library.IsNull()) {
      existing_libraries.Add(library);  // 已存在的库
    } else {
      new_libraries.Add(uri);           // 新库
    }
  }
  
  // 分别处理新库和已存在的库
  ProcessNewLibraries(new_libraries);
  ProcessExistingLibraries(existing_libraries);
}
```

### 2. 增量类更新

```cpp
void BytecodeReaderHelper::ReadLibraryDeclarationIncremental(
    const Library& library,
    const GrowableObjectArray& pending_classes) {
  
  for (intptr_t i = 0; i < num_classes; ++i) {
    class_name ^= ReadObject();
    cls = library.LookupClassAllowPrivate(class_name);
    
    if (cls.IsNull()) {
      // 新类 - 创建它
      cls = Class::New(library, class_name, script, TokenPosition::kNoSource);
      library.AddClass(cls);
    } else {
      // 已存在的类 - 检查是否需要更新
      if (NeedsUpdate(cls, class_offset)) {
        ResetClassForReload(cls);
      }
    }
  }
}
```

### 3. 新的加载入口点

```cpp
FunctionPtr BytecodeLoader::LoadBytecodeIncremental() {
  // 使用增量加载逻辑
  // 1. 读取组件
  // 2. 增量处理库声明
  // 3. 返回主函数
}
```

## 使用方法

### 原始方式（会失败）
```cpp
// 这会导致 "library already loaded" 错误
BytecodeLoader loader(thread, binary);
FunctionPtr main = loader.LoadBytecode();
```

### 新的混合方式
```cpp
// 使用增量加载
BytecodeLoader loader(thread, binary);
FunctionPtr main = loader.LoadBytecodeIncremental();
```

## 技术细节

### 库状态管理

1. **新库**: 完全按照原始流程加载
2. **已存在的库**: 
   - 跳过基本属性设置（名称、脚本等）
   - 增量更新类定义
   - 保持现有的加载状态

### 类更新策略

1. **新类**: 创建并添加到库中
2. **已存在的类**:
   - 比较字节码偏移量
   - 如果有变化，重置声明状态
   - 允许重新加载字节码

### 安全性保证

1. **状态一致性**: 保持 AOT 和 bytecode 数据的一致性
2. **引用完整性**: 不破坏现有的对象引用
3. **类型安全**: 确保类型系统的正确性

## 与 isolate_reload 的对比

| 特性 | isolate_reload | 混合加载 |
|------|----------------|----------|
| 目标 | 热重载现有代码 | 合并 AOT + bytecode |
| 库处理 | 替换现有库 | 增量添加到现有库 |
| 实例变形 | 支持 | 不需要 |
| 回滚机制 | 支持 | 不需要 |
| 复杂度 | 高 | 中等 |

## 限制和注意事项

### 1. 兼容性限制
- bytecode 中的类不能与 AOT 中的类冲突
- 方法签名必须兼容
- 不能删除 AOT 中已有的公共 API

### 2. 性能考虑
- 增量检查会增加一些开销
- 大量类更新时的内存压力
- 符号查找的额外成本

### 3. 调试支持
- 混合模式下的调试信息合并
- 源码映射的正确性
- 断点设置的兼容性

## 未来改进

### 1. 更智能的冲突检测
```cpp
bool IsCompatible(const Class& aot_class, const Class& bytecode_class) {
  // 检查方法签名兼容性
  // 检查字段布局兼容性
  // 检查继承关系兼容性
}
```

### 2. 选择性加载
```cpp
struct LoadingOptions {
  bool skip_existing_libraries = true;
  bool update_existing_classes = true;
  bool merge_debug_info = true;
};
```

### 3. 性能优化
- 缓存库查找结果
- 批量处理类更新
- 延迟加载非关键组件

## 总结

这个混合加载解决方案通过借鉴 `isolate_reload` 的增量更新思想，成功解决了 AOT + bytecode 的兼容性问题。它提供了一个安全、高效的方式来扩展 AOT 运行时的功能，同时保持了系统的稳定性和性能。

关键优势：
- ✅ 解决库重复加载问题
- ✅ 保持 AOT 性能优势
- ✅ 支持 bytecode 的完整功能
- ✅ 提供增量更新能力
- ✅ 维护类型安全性

这为构建更灵活的 Dart 运行时环境奠定了基础。

## 实际应用场景

### 1. 插件系统
```cpp
// 动态加载插件的字节码
void LoadPlugin(const std::string& plugin_path) {
  auto bytecode = ReadBytecodeFile(plugin_path);
  Dart_LoadBytecodeOnAOT(bytecode.data(), bytecode.size());
}
```

### 2. 热更新系统
```cpp
// 在 AOT 应用中实现热更新
void HotUpdate(const std::vector<uint8_t>& update_bytecode) {
  // 增量加载新的业务逻辑
  auto result = Dart_LoadBytecodeOnAOT(update_bytecode.data(), update_bytecode.size());
  if (!Dart_IsError(result)) {
    // 通知应用更新完成
    NotifyUpdateComplete();
  }
}
```

### 3. 开发时调试
```cpp
// 在 AOT 模式下加载调试版本的代码
void LoadDebugCode() {
  if (IsDebugMode()) {
    auto debug_bytecode = LoadDebugBytecode();
    Dart_LoadBytecodeOnAOT(debug_bytecode.data(), debug_bytecode.size());
  }
}
```

## 性能对比

| 场景 | 纯 AOT | 纯 Bytecode | 混合模式 |
|------|--------|-------------|----------|
| 启动时间 | 最快 | 慢 | 快 |
| 运行时性能 | 最快 | 慢 | 快（AOT部分）+ 慢（Bytecode部分）|
| 内存占用 | 大 | 小 | 中等 |
| 灵活性 | 低 | 高 | 高 |
| 更新能力 | 无 | 有 | 有 |

## 最佳实践

1. **核心功能使用 AOT**: 将性能关键的代码编译为 AOT
2. **扩展功能使用 Bytecode**: 将可变的、插件化的代码保持为 bytecode
3. **渐进式迁移**: 逐步将稳定的 bytecode 代码迁移到 AOT
4. **监控性能**: 定期检查混合模式的性能表现
