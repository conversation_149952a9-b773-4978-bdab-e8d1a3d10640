Welcome to the developer documentation for the Dart SDK!

[Contributions](../CONTRIBUTING.md) welcome! Please help us keep these docs
up-to-date.

## Important pages

Important pages include:

- [Building the SDK](Building.md)
- [Filing Dart issues](Filing-Dart-issues.md)
- Workarounds for [Flutter Pinned Packages](Flutter-Pinned-Packages.md)
- [How the issue tracker works](How-the-issue-tracker-works.md)
- [Testing](Testing.md)

Also, check out the combined
[Dart and Flutter roadmap](https://github.com/flutter/flutter/blob/master/docs/roadmap/Roadmap.md)
on the Flutter wiki.

## Process pages

Various process pages include:

- [Dart SDK breaking change process](process/breaking-changes.md)
- [Dart SDK process for changes behind experimental flags](process/experimental-flags.md)
- [Language Versioning and Experiments](process/language-versions-and-experiments.md)

## Contributing

Contributions welcome via Gerrit CLs or GitHub PRs. See our
[contributing](../CONTRIBUTING.md) doc for more information.

Contributions could include helping to keep pages up-to-date, adding additional
context to this main page, or re-organizing the documentation.

## Google Summer of Code

Interested in the latest on our Google Summer of Code projects? See
[Google Summer of Code 2024](gsoc/Dart-GSoC-2024-Project-Ideas.md).
