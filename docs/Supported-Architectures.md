> [!IMPORTANT]
> This page was copied from https://github.com/dart-lang/sdk/wiki and needs review.
> Please [contribute](../CONTRIBUTING.md) changes to bring it up-to-date -
> removing this header - or send a CL to delete the file.

---

| Operating System | JIT | AOT |
| ------------- | ------------- | ----- |
| Android  | ia32, x64, armv7, armv8, rv64gc | x64, armv7, armv8, rv64gc  |
| Fuchsia | x64, armv8, rv64gc | x64, armv8, rv64gc |
| iOS  | armv7, armv8  | armv7, armv8 |
| Linux  | ia32, x64, armv7, armv8, rv32gc, rv64gc  | x64, armv7, armv8, rv32gc, rv64gc |
| macOS  | x64, armv8  | x64, armv8 |
| Windows | ia32, x64, armv8 | x64, armv8 |

Past versions of the Dart VM also supported mipsel, armv6 and armv5te.
