> [!warning]
> **Google Summer of Code 2024 is no longer accepting applications**.

------

A list of Google Summer of Code project ideas for Dart.

For GSoC related discussions please use the [dart-gsoc group](https://groups.google.com/forum/#!forum/dart-gsoc).

**Potential mentors**
 * <PERSON> ([jonasfj](https://github.com/jonasfj)) `<EMAIL>`
 * <PERSON><PERSON> ([dcharkes](https://github.com/dcharkes)) `<EMAIL>`
 * Hossein <PERSON>‎ ([HosseinYousefi](https://github.com/HosseinYousefi)) `<EMAIL>`
 * Chinmoy Chakraborty ([chinmoy12c](https://github.com/chinmoy12c)) `<EMAIL>`
 * <PERSON> ([liamappelbe](https://github.com/liamappelbe)) `<EMAIL>`
 * <PERSON><PERSON> ([mannprerak2](https://github.com/mannprerak2)) `<EMAIL>`
 * <PERSON> ([eyebrowsoffire](https://github.com/eyebrowsoffire)) `<EMAIL>`
 * <PERSON> ([camsim99](https://github.com/camsim99)) `<EMAIL>`
 * Brian Quinlan ([bquinlan](https://github.com/brianquinlan)) `<EMAIL>`

## Project Application Process
All projects assume familiarity with Dart (and sometimes Flutter). Aspiring applicants are encouraged to [learn Dart](https://dart.dev/guides/language/language-tour) and try to write some code.

Applicants are welcome to find and fix bugs in [Dart](https://github.com/dart-lang/sdk) or some of the [packages written by the Dart team](https://pub.dev/publishers/dart.dev/packages). However, getting reviews can take a long time as code owners may be busy working on new features. So instead of requiring applicants to fix a _good first bug_, we
suggest that applicants write a working code sample relevant for the proposed project.

The code sample can be attached to the application as a [**secret** gist](https://gist.github.com/) (please use _secret gists_, and do not share these with other applicants). Suggested ideas below includes proposed "Good Sample Projects".

**Do not spend too much energy on this piece of sample code**, we just want to see
that you can code something relevant -- and that this sample code can run and do something non-trivial. Be aware that we have a limited number of
mentors available, and will only be able to accept a few applicants.

Applications can be submitted through the [summerofcode.withgoogle.com](https://summerofcode.withgoogle.com/) website. Applicants are encouraged to submit draft proposals, linking to Google Docs with permission for mentors to comment. See also the [contributor guide](https://google.github.io/gsocguides/student/writing-a-proposal) on writing a proposal.

**IMPORTANT**: Remember to submit _final proposals_ before [the April 2nd deadline](https://developers.google.com/open-source/gsoc/timeline).

## **Idea:** FFIgenpad

 - **Possible Mentor(s)**: @dcharkes @mannprerak2 @eyebrowsoffire
 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: C, WASM, Web development, Dart

**Description**:

Original idea: A web interface in which you paste a C header in a textbox on the left, and it outputs `dart:ffi` bindings generated by `package:ffigen` in a textbox on the right. (Inspiration is https://godbolt.org/ which does this for a C file on the left and assembly on the right.)

In order to avoid needing a server to do the backend, we'd like to compile the Dart code in `package:ffigen` and `libclang` to WASM. That way everything can run in the browser.

**Good Sample Project**:

* Compile some C code to WASM and call it from Dart (non-Flutter) web app compiled to WASM.
* Explore compiling libclang from the llvm repository to WASM.

For the purposes of this project, we decided that direct linking is going to make more sense for interfacing with libclang. It should be noted that direct linking will not actually work with a Flutter app, since wasm-compiled flutter apps already link against a separate wasm module for rendering and you cannot link more than one linear memory to the dart2wasm compiled module.

The recommended approach is going to be something like the following:
* Compile a small wasm module via emscripten with a single C function that will be called from dart code
* The Dart code can use the `dart:ffi` and `@Native` annotations to import the C function and call it.
* The Dart code can also use `package:web` to interact with the DOM, in order to input and output information to the user.
* Compile the Dart code to WebAssembly using `dart compile wasm`. The compiler outputs a `main.dart.mjs` file that is produced alongside the `main.dart.wasm` file. This is used to instantiate and invoke the webassembly-compiled dart program. Usage in JavaScript should look something like this:

```
import 'main.dart.mjs' as dartProgram;

const imports = ...; // Do something here to instantiate the external C module so its exports can be passed in as imports
const module = await dartProgram.instantiate(WebAssembly.compileStreaming(fetch('main.dart.wasm')), imports);
dartProgram.invoke(module);
```

Getting all this working and communicating would be a good first step. After that we'd probably want to look into doing some simple virtual filesystem access in the C module, as `libclang` will need access to the source files.

**Expected outcome**: A web app (possibly hosted on github.io).

## **Idea:** Swift/ObjC compatibility tool

 - **Possible Mentor(s)**: @liamappelbe
 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: Swift

**Description**:

`package:ffigen` allows Dart to interact with ObjC. Swift modules can be invoked from ObjC (and Dart through ffi),
but only if the classes and methods have been annotated with `@objc`. So if a user wants to interact with a Swift
module they don't own from Dart, they need to write a wrapper module that mirrors the API, but has `@objc`
annotations.

It should be possible to write a tool that can automatically generate this ObjC compatibility wrapper. The official
[Swift parsing library](https://github.com/apple/swift-syntax) is written in Swift, so the tool should probably
also be written in Swift. In fact, the candidate doesn't even really need any Dart experience, just Swift and ObjC.

**Good Sample Project**: Use the Swift parsing library to load a module and print some of its metadata (e.g.
print a list of the classes in the module).

A great project proposal will also talk about which Swift language features can be feasibly supported by this tool.
For example, is it possible to write an `@objc` wrapper method for an `async` Swift method? What would that look
like? Swift is a modern language, and ObjC is not, so there are probably a lot of features that will be problematic
to wrap in a compatibility class.

**Expected outcome**: A dart package available on [pub.dev](https://pub.dev/).

## **Idea:** JNIgen transformations

 - **Possible Mentor(s)**: `<EMAIL>`
 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: Dart, Java

**Description**: With JNIgen, we bridge between Dart and Java by generating Dart bindings for Java classes. Sometimes however, the semantic differences between the languages means that we want to make some changes to these bindings. For example, accessing the `i`th element of an `List` in Java is done by `list.get(i)` but in Dart we can do `list[i]`. To facilitate these changes, we want to introduce a transformation mechanism.

Specifically we want to add the ability to rename, exclude, convert to operators, add members, and generally transform the classes and methods using visitor pattern. Something like this:

```dart
Config(
  visitors: [Foo2BarRenamer()], // User passes the visitor to config
);

class Foo2BarRenamer extends JnigenVisitor {
  void visitClass(Class c) {
    if (c.name == 'foo') c.name = 'bar';
    c.visitMethods(this);
    // ...
  }
  // ...
}

```

**Good Sample Project**: Create a simple renamer/excluder visitor, so JNIgen users can pass that in, and rename/exclude classes, methods, and fields. You will have to create a user-facing AST that wraps the original AST. For the sample project, the wrapper AST could only expose `name` or `included` properties.

Feel free to email me and ask further questions about the project!

**Expected outcome**: A new set of features for [`package:jnigen`](https://pub.dev/packages/jnigen).

## **Idea:** Integrate OkHttp with Dart

 - **Possible Mentor(s)**: `<EMAIL>`, `<EMAIL>`

 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: Dart, Java, Android

**Description**: Write a flutter plugin that wraps the [OkHttp](https://square.github.io/okhttp/) package using [`package:jnigen`](https://pub.dev/packages/jnigen) and implements the [`package:http` `Client` interface](https://pub.dev/documentation/http/latest/http/Client-class.html). Time permitting, the plugin will also implement the [`package:web_socket_channel` `WebSocketChannel` interface](https://pub.dev/documentation/web_socket_channel/latest/web_socket_channel/WebSocketChannel-class.html). This will allow us to provide several features requested by our users such as:

 * Support for `KeyStore` `PrivateKey`s ([#50669](https://github.com/dart-lang/sdk/issues/50669))
 * Support for the system proxy ([#50434](https://github.com/dart-lang/sdk/issues/50434))
 * Support for user-installed certificates ([#50435](https://github.com/dart-lang/sdk/issues/50435))

Successfully completely this project will likely involve:

  * Determining exactly what APIs should be make available in Dart.
  * Creating a JNI bindings for those APIs using [`package:jnigen`](https://pub.dev/packages/jnigen).
  * Creating a [`package:http` `Client`](https://pub.dev/documentation/http/latest/http/Client-class.html) implementation using those bindings.
  * Verifying that the `Client` implementation passes the [conformance tests](https://github.com/dart-lang/http/tree/master/pkgs/http_client_conformance_tests).

You'll like working on this project because:

 * It will be easy to implement incrementally. After the basic functionality is present, more advanced APIs can be added as time permits.
 * There are existing [conformance tests](https://github.com/dart-lang/http/tree/master/pkgs/http_client_conformance_tests) to validate the correctness of the work.
 * Dart users want it!

A good project proposal will describe what Java APIs are necessary to implement the [`package:http` `Client` interface](https://pub.dev/documentation/http/latest/http/Client-class.html) e.g. [`OkHttpClient.newCall()`](https://square.github.io/okhttp/5.x/okhttp/okhttp3/-ok-http-client/new-call.html). An *excellent* proposal will use asynchronous APIs.

**Good Sample Project**: Try writing a small [Flutter](https://flutter.dev/) application that makes HTTP requests using OkHttp bindings created with [`package:jnigen`](https://pub.dev/packages/jnigen). You can use [`package:cronet_http`](https://github.com/dart-lang/http/tree/master/pkgs/cronet_http) and [`package:java_http`](https://github.com/dart-lang/http/tree/master/pkgs/java_http) as starting points. 

> [!TIP]
> Look at the [`package:cronet_http`](https://github.com/dart-lang/http/tree/master/pkgs/cronet_http)
> [jnigen.yaml](https://github.com/dart-lang/http/blob/master/pkgs/cronet_http/jnigen.yaml) and 
> [build.gradle](https://github.com/dart-lang/http/blob/master/pkgs/cronet_http/example/android/app/build.gradle#L76) files.

**Expected outcome**: A dart package available on [pub.dev](https://pub.dev/).

## **Idea:** Exception testing for `package:webcrypto`

 - **Possible Mentor(s)**: `<EMAIL>`, `<EMAIL>`
 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: Dart, FFI, JS

**Description**: `package:webcrypto` ([github.com/google/webcrypto.dart](https://github.com/google/webcrypto.dart)) is a cross-platform implementation of the [Web Cryptography API](https://www.w3.org/TR/WebCryptoAPI/).
It is important that it behaves the same way whether it's running on Windows, Linux, Mac, Android, iOS, Chrome, Firefox, or Safari. Towards that end, it has a lot of test cases. We could and should probably make more test cases.
But we should also test that it throws the types of exceptions when given incorrect parameters. This probably needs a small test framework to ensure good test coverage.

We expect a proposal for this project to include:
 * A sample showing how to test exceptions for `RsaPssPrivateKey.generateKey`.
   Ideally, the sample project includes parts of a generalized framework for testing exceptions.
 * An outline of what kind of exceptions should be tested?
 * A design for extending `TestRunner`, or creating a new framework, to test exceptions thrown by all methods.
   * Illustrative code for how test cases would be configured
   * Pros and cons of the design (especially when multiple choices are available)
 * Timeline for the project

**Good Sample Project**: 
Write a test cases that tests the different kinds of errors and exceptions that can be thrown by `RsaPssPrivateKey.generateKey`, run the tests across desktop, Chrome and Firefox. Consider extending the tests to cover all members of `RsaPssPrivateKey`.
Try to generalize these test cases to avoid repetitive code, see the existing [TestRunner](https://github.com/google/webcrypto.dart/blob/5e6d20f820531d2b7b05935c1d78f38a036035e8/lib/src/testing/utils/testrunner.dart#L227) for inspiration.


**Expected outcome**: PRs that land in `package:webcrypto` and increases our confidence in correctness cross-platforms.

## **Idea:** Testing documentation comments

 - **Possible Mentor(s)**: `<EMAIL>`, `<EMAIL>`
 - **Difficulty**: Hard
 - **Project size**: Large (350 hours)
 - **Skills**: Dart, static analysis

**Description**: When writing Dart code it is useful to write
[documentation comments](https://dart.dev/guides/language/effective-dart/documentation#doc-comments),
such comments will be included in automatically generated documentation created by `dartdoc`.
Documentation comments for `dartdoc` are written in markdown, which allows authors to
embed [code samples](https://dart.dev/guides/language/effective-dart/documentation#consider-including-code-samples-in-doc-comments).
This project aims to create tools for testing _code samples_ embedded in documentation comments.

This will likely involve:
 * Using `package:analyzer` to extract documentation comments.
 * Using `package:markdown` to extract code samples.
 * Testing these code samples by:
   * Running `dart analyze` on the code sample,
   * Passing the code sample through `dart format`, and/or,
   * Running the code sample in an isolate and compare stdout to comments from the sample.

For this project, we'll finish the [`dartdoc_test`](https://pub.dev/packages/dartdoc_test) package, such that it can be used by
package authors who wish to test code samples in their documentation comments.

As part of this project, we'll likely have to define conventions for what is expected of a
code sample in documentation comments:

 * What libraries (if any) are implicitly imported?
 * Can you make code samples that are excluded from testing?
 * Can comments inside the code sample be used to indicate expected output in stdout?
 * How should code be written?
    * Do all code samples need a `main` function?
    * Do we wrap top-level code in an implicit `main` to keep it simple?
    * Do we run the top-level function if it has a name other than `main`?
 * Do we allow dead-code in samples (without an `// ignore: unreachable` comment)?
 * What lints do we apply to sample code (same as the top-level project).

Some of these questions might be debated in the project proposal.
A project proposal should also discuss how package authors would run the code sample tests.
Finally, a project proposal is encouraged to outline implementation stages, including stretch goals.

**Good Sample Project**: Create a function that given some Dart code will use `package:analyzer` to do static analysis of the code and count static errors. Additional step would be to try and use `package:analyzer` to extract documentation comments from source code and use `package:markdown` to extract code-snippets from source code comments, and then run analysis on the extracted source code snippets. Ideally, all of this could be done, in-memory without writing files to disk.

Useful hints: [analyzer tutorial](https://github.com/dart-lang/sdk/tree/main/pkg/analyzer/doc/tutorial) | [analyzer API docs](https://pub.dev/documentation/analyzer/latest/) | [analyzer file_system abstraction](https://pub.dev/documentation/analyzer/latest/file_system_file_system/file_system_file_system-library.html).



# Template:

Copy this template.

## **Idea:** ...

 - **Possible Mentor(s)**: 
 - **Difficulty**: Easy / Hard
 - **Project size**: Small (90) / Medium (175 hours) / Large (350 hours)
 - **Skills**: ...

**Description**: ...

**Good Sample Project**: ...

**Expected outcome**: ...
