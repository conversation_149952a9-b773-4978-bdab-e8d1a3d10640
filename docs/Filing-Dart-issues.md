> [!IMPORTANT]
> This page was copied from https://github.com/dart-lang/sdk/wiki and needs review.
> Please [contribute](../CONTRIBUTING.md) changes to bring it up-to-date -
> removing this header - or send a CL to delete the file.

---

Please file issues found in Dar<PERSON> using GitHub issue tracking. However, please note that Dart is being developed in several GitHub repos, so please make sure to file the issue in the correct repo. Consult the table below for details.

Component | Issue tracker
--------- | -------------
SDK core (language, VM, dart2js, Analyzer, Debugger/Observatory) |  [dart-lang/sdk](https://github.com/dart-lang/sdk/issues)
DartPad editor | [dart-lang/dart-pad](https://github.com/dart-lang/dart-pad/issues)
Pub tool | [dart-lang/pub](https://github.com/dart-lang/pub/issues)
dartfmt tool | [dart-lang/dart_style](https://github.com/dart-lang/dart_style/issues)
Dartdoc tool | [dart-lang/dartdoc](https://github.com/dart-lang/dartdoc/issues)
test library | [dart-lang/test](https://github.com/dart-lang/test/issues)
AngularDart | [dart-lang/angular](https://github.com/dart-lang/angular/issues)
www.dartlang.org website | [dart-lang/site-www](https://github.com/dart-lang/site-www/issues)
