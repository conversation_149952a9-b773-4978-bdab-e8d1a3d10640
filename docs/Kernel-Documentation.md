> [!IMPORTANT]
> This page was copied from https://github.com/dart-lang/sdk/wiki and needs review.
> Please [contribute](../CONTRIBUTING.md) changes to bring it up-to-date -
> removing this header - or send a CL to delete the file.

---

Dart Kernel is an intermediate language for Dart programs, a compact binary object-file format supporting separate compilation and linking, and an infrastructure for program transformation.  This page is an index of Kernel documentation.

[Binary format](https://github.com/dart-lang/sdk/blob/main/pkg/kernel/binary.md)
[Operational semantics](Kernel-Operational-Semantics.md)
[Type System](Kernel-Type-System.md)
