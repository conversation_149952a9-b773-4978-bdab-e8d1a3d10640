{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(grep -n -B5 -A5 \"PrivateName\" runtime/vm/bytecode_reader.cc)", "Bash(find /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm -name \"*.cc.orig\" -o -name \"*.cc.backup\")", "Bash(sed -i '2410,3401d' /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(sed -i '' '2410,3401d' /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(grep -n \"void BytecodeReaderHelper::ReadLibraryDeclarationIncremental\" /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(grep -n \"void BytecodeReaderHelper::ReadParameterCovariance\" /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(grep -n \"ReadMembersIncremental\" /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(grep -n -A 15 \"void BytecodeReader::FinishClassLoading\" /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader.cc)", "Bash(rm /Users/<USER>/Tencent/flutter_stable/flutter/engine/src/flutter/third_party/dart-sdk/sdk/runtime/vm/bytecode_reader_clean.cc)"], "deny": []}}