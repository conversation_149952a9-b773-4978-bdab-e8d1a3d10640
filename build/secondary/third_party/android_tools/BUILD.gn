# Copyright 2018 The Dart project Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")

config("cpu_features_include") {
  include_dirs = [ "$android_ndk_root/sources/android/cpufeatures" ]
}

config("cpu_features_warnings") {
  if (is_clang) {
    # cpu-features.c has few unused functions on x86 b/26403333
    cflags = [ "-Wno-unused-function" ]
  }
}

source_set("cpu_features") {
  sources = [ "$android_ndk_root/sources/android/cpufeatures/cpu-features.c" ]
  public_configs = [ ":cpu_features_include" ]
  configs -= [ "//build/config/compiler:chromium_code" ]
  configs += [
    "//build/config/compiler:no_chromium_code",

    # Must be after no_chromium_code for warning flags to be ordered correctly.
    ":cpu_features_warnings",
  ]
}
