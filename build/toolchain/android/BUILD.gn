# Copyright 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/toolchain/toolchain_suite.gni")

template("android_toolchain_suite") {
  toolchain_suite(target_name) {
    toolchain_template = "android_toolchain"
    forward_variables_from(invoker, "*")
  }
}

android_toolchain_suite("clang_x86") {
  toolchain_cpu = "x86"
}

android_toolchain_suite("clang_arm") {
  toolchain_cpu = "arm"
}

android_toolchain_suite("clang_x64") {
  toolchain_cpu = "x64"
}

android_toolchain_suite("clang_arm64") {
  toolchain_cpu = "arm64"
}

android_toolchain_suite("clang_riscv64") {
  toolchain_cpu = "riscv64"
}
