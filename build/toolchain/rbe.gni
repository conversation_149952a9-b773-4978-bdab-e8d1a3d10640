# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Defines the configuration of RBE.

_dart_root = get_path_info("../..", "abspath")

declare_args() {
  # Set to true to enable distributed compilation using RBE.
  use_rbe = false

  # Set to the operating system to use in the RBE pool
  rbe_os = "linux"

  # Set to the architecture used in the RBE pool.
  rbe_cpu = "x64"

  # Set to the docker image to use in the RBE.
  rbe_image = "docker://gcr.io/cloud-marketplace/google/debian11@sha256:69e2789c9f3d28c6a0f13b25062c240ee7772be1f5e6d41bb4680b63eae6b304"

  # Do expensive RBE actions remotely, falling back to local on failure/timeout.
  # The "racing" strategy can cause a memory exhaustion problem if too many
  # actions run locally at the same time.
  rbe_expensive_exec_strategy = "remote_local_fallback"

  rbe_exec_root = rebase_path("//", root_build_dir)

  if (host_os == "win") {
    rbe_dir =
        rebase_path(_dart_root + "/buildtools/reclient-win", root_build_dir)
  } else if (host_os == "linux") {
    rbe_dir =
        rebase_path(_dart_root + "/buildtools/reclient-linux", root_build_dir)
  } else {
    rbe_dir = rebase_path(_dart_root + "/buildtools/reclient", root_build_dir)
  }
}

declare_args() {
  if (rbe_os == "linux") {
    rbe_os_family = "Linux"
  } else if (rbe_os == "win") {
    rbe_os_family = "Windows"
  } else if (rbe_os == "mac") {
    rbe_os_family = "Mac"
  } else {
    rbe_os_family = rbe_os
  }

  # Set to the desired fully qualified RBE platform.
  rbe_platform = "container-image=$rbe_image,OSFamily=$rbe_os_family"
}

rewrapper_args = [
  "$rbe_dir/rewrapper",
  "--exec_root=$rbe_exec_root",
  "--platform=$rbe_platform",
]
