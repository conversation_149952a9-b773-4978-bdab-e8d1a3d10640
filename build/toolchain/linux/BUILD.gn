# Copyright 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/sysroot.gni")
import("//build/toolchain/ccache.gni")
import("//build/toolchain/gcc_toolchain.gni")
import("//build/toolchain/rbe.gni")
import("//build/toolchain/toolchain_suite.gni")

declare_args() {
  ia32_toolchain_prefix = ""
  x64_toolchain_prefix = ""
  arm_toolchain_prefix = ""
  arm64_toolchain_prefix = ""
  riscv32_toolchain_prefix = ""
  riscv64_toolchain_prefix = ""
}

if (use_rbe) {
  compiler_args =
      rewrapper_args + [ "--labels=type=compile,compiler=clang,lang=cpp" ]

  # TODO: Unfortunately I see no way to get build_arch reliably.
  if (rbe_os != host_os || rbe_cpu != host_cpu) {
    compiler_args += [
      "--inputs=build/rbe,buildtools/$rbe_os-$rbe_cpu/clang/bin/llvm",
      "--remote_wrapper=../../build/rbe/llvm.sh",
    ]
  }
  assembler_prefix = ""
  compiler_prefix = string_join(" ", compiler_args) + " "
  link_prefix = ""
  gcc_compiler_prefix = ""
} else if (use_ccache) {
  assembler_prefix = "ccache "
  compiler_prefix = "ccache "
  link_prefix = "ccache "
  gcc_compiler_prefix = compiler_prefix
} else {
  assembler_prefix = ""
  compiler_prefix = ""
  link_prefix = ""
  gcc_compiler_prefix = compiler_prefix
}

# Google's clang does not work for alpine, use alpine's system clang
if (dart_sysroot == "alpine") {
  rebased_clang_dir = "/usr/bin"
} else if (host_cpu == "arm64") {
  rebased_clang_dir =
      rebase_path("//buildtools/linux-arm64/clang/bin", root_build_dir)
} else {
  rebased_clang_dir =
      rebase_path("//buildtools/linux-x64/clang/bin", root_build_dir)
}

template("gcc_toolchain_suite") {
  toolchain_suite(target_name) {
    toolchain_template = "gcc_toolchain"
    forward_variables_from(invoker, "*")
  }
}

gcc_toolchain_suite("arm") {
  prefix = "arm-linux-gnueabihf-"
  if (arm_toolchain_prefix != "") {
    prefix = arm_toolchain_prefix
  }

  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  strip = "${prefix}strip"

  toolchain_cpu = "arm"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("clang_arm") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=arm-linux-gnueabihf"
    cxx = "${cxx} --target=arm-linux-gnueabihf"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "arm"
  toolchain_os = "linux"
  is_clang = true
}

gcc_toolchain_suite("arm64") {
  prefix = "aarch64-linux-gnu-"
  if (arm64_toolchain_prefix != "") {
    prefix = arm64_toolchain_prefix
  }

  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  strip = "${prefix}strip"

  toolchain_cpu = "arm64"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("clang_arm64") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=aarch64-linux-gnu"
    cxx = "${cxx} --target=aarch64-linux-gnu"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "arm64"
  toolchain_os = "linux"
  is_clang = true
}

gcc_toolchain_suite("clang_x86") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=i386-linux-gnu"
    cxx = "${cxx} --target=i386-linux-gnu"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "x86"
  toolchain_os = "linux"
  is_clang = true
}

gcc_toolchain_suite("x86") {
  prefix = "i686-linux-gnu-"
  if (ia32_toolchain_prefix != "") {
    prefix = ia32_toolchain_prefix
  }
  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  strip = "${prefix}strip"

  toolchain_cpu = "x86"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("clang_x64") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=x86_64-linux-gnu"
    cxx = "${cxx} --target=x86_64-linux-gnu"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "x64"
  toolchain_os = "linux"
  is_clang = true
}

gcc_toolchain_suite("x64") {
  prefix = "x86_64-linux-gnu-"
  if (x64_toolchain_prefix != "") {
    prefix = x64_toolchain_prefix
  }
  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  strip = "${prefix}strip"

  toolchain_cpu = "x64"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("riscv32") {
  prefix = "riscv32-linux-gnu-"
  if (riscv32_toolchain_prefix != "") {
    prefix = riscv32_toolchain_prefix
  }

  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  strip = "${prefix}strip"

  toolchain_cpu = "riscv32"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("clang_riscv32") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=x86_64-linux-gnu"
    cxx = "${cxx} --target=x86_64-linux-gnu"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "riscv32"
  toolchain_os = "linux"
  is_clang = true
}

gcc_toolchain_suite("riscv64") {
  prefix = "riscv64-linux-gnu-"
  if (riscv64_toolchain_prefix != "") {
    prefix = riscv64_toolchain_prefix
  }

  cc = "${gcc_compiler_prefix}${prefix}gcc"
  cxx = "${gcc_compiler_prefix}${prefix}g++"
  asm = "${gcc_compiler_prefix}${prefix}gcc"

  ar = "${prefix}ar"
  ld = "${link_prefix}${prefix}g++"
  readelf = "${prefix}readelf"
  nm = "${prefix}nm"
  strip = "${prefix}strip"

  toolchain_cpu = "riscv64"
  toolchain_os = "linux"
  is_clang = false
}

gcc_toolchain_suite("clang_riscv64") {
  prefix = rebased_clang_dir
  cc = "${compiler_prefix}${prefix}/clang"
  cxx = "${compiler_prefix}${prefix}/clang++"
  asm = "${assembler_prefix}${prefix}/clang"

  if (use_rbe) {
    cc = "${cc} --target=riscv64-linux-gnu"
    cxx = "${cxx} --target=riscv64-linux-gnu"
  }

  readelf = "${prefix}/llvm-readelf"
  nm = "${prefix}/llvm-nm"
  ar = "${prefix}/llvm-ar"
  ld = "${link_prefix}${prefix}/clang++"
  llvm_objcopy = "${prefix}/llvm-objcopy"

  toolchain_cpu = "riscv64"
  toolchain_os = "linux"
  is_clang = true
}
