# Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
# for details. All rights reserved. Use of this source code is governed by a
# BSD-style license that can be found in the LICENSE file.

# Defines code signing configuration for Mac builds.

declare_args() {
  # If codesigning_identity is not empty then all executables will be
  # signed using the specified identity and using corresponding entitlements
  # from runtime/tools/entitlements/${binary_name}.plist.
  #
  # You can specify codesigning_identity = "-" to use ad-hoc codesigning.
  #
  # See also runtime/tools/dart_codesigning.py script.
  codesigning_identity = ""
}
