# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/win/visual_studio_version.gni")
import("//build/toolchain/rbe.gni")

# Compiler setup for the Windows SDK. Applied to all targets.
config("sdk") {
  # The include path is the stuff returned by the script.
  #include_dirs = msvc_config[0]  TODO(brettw) make this work.

  defines = [
    "_ATL_NO_OPENGL",
    "_WINDOWS",
    "CERT_CHAIN_PARA_HAS_EXTRA_FIELDS",
    "NTDDI_VERSION=0x0A000006",
    "PSAPI_VERSION=1",
    "WIN32",
    "_SECURE_ATL",

    # This is required for ATL to use XP-safe versions of its functions.
    "_USING_V110_SDK71_",
  ]
}

# Sets the default Windows build version. This is separated because some
# targets need to manually override it for their compiles.
config("winver") {
  defines = [
    "_WIN32_WINNT=0x0603",
    "WINVER=0x0603",
  ]
}

# Linker flags for Windows SDK setup, this is applied only to EXEs and DLLs.
config("sdk_link") {
  if (current_cpu == "x86") {
    ldflags = [
      "/MACHINE:X86",
      "/SAFESEH",  # Not compatible with x64 so use only for x86.
    ]
    lib_dirs = [
      "$windows_sdk_path\Lib\winv6.3\um\x86",
      "$visual_studio_path\VC\lib",
      "$visual_studio_path\VC\atlmfc\lib",
    ]
    if (!is_asan) {
      ldflags += [ "/largeaddressaware" ]
    }
  } else if (current_cpu == "x64") {
    ldflags = [ "/MACHINE:X64" ]
    lib_dirs = [
      "$windows_sdk_path\Lib\winv6.3\um\x64",
      "$visual_studio_path\VC\lib\amd64",
      "$visual_studio_path\VC\atlmfc\lib\amd64",
    ]
  } else if (current_cpu == "arm") {
    ldflags = [ "/MACHINE:ARM" ]
    lib_dirs = [
      "$windows_sdk_path\Lib\winv6.3\um\arm",
      "$visual_studio_path\VC\lib\arm",
      "$visual_studio_path\VC\atlmfc\lib\arm",
    ]
  } else if (current_cpu == "arm64") {
    ldflags = [ "/MACHINE:ARM64" ]
    lib_dirs = [
      "$windows_sdk_path\Lib\winv6.3\um\arm64",
      "$visual_studio_path\VC\lib\arm64",
      "$visual_studio_path\VC\atlmfc\lib\arm64",
    ]
  } else {
    assert(false, "Unknown current_cpu: $current_cpu")
  }
}

# This default linker setup is provided separately from the SDK setup so
# targets who want different library configurations can remove this and specify
# their own.
config("common_linker_setup") {
  ldflags = [
    "/FIXED:NO",
    "/ignore:4199",
    "/ignore:4221",
    "/ignore:4197",  # Disable multiple Dart_True export warning.
    "/NXCOMPAT",
    "/DYNAMICBASE",

    # Embed default manifest, which sets the requested UAC level to asInvoker,
    # to prevent "installer detection" from automatically deciding to request
    # elevated privileges (https://learn.microsoft.com/en-us/windows/security/application-security/application-control/user-account-control/how-it-works#installer-detection-technology).
    "/MANIFEST:EMBED",

    # Suggested by Microsoft Devrel to avoid
    #   LINK : fatal error LNK1248: image size (********)
    #   exceeds maximum allowable size (********)
    # which started happening more regularly after VS2013 Update 4.
    "/maxilksize:**********",
  ]
}

# Subsystem -------------------------------------------------------------------

# This is appended to the subsystem to specify a minimum version.
if (current_cpu == "x86" || current_cpu == "x64" || current_cpu == "arm" ||
    current_cpu == "arm64") {
  subsystem_version_suffix = ",10.0"
} else {
  assert(false, "Unknown current_cpu: $current_cpu")
}

config("console") {
  ldflags = [ "/SUBSYSTEM:CONSOLE$subsystem_version_suffix" ]
}
config("windowed") {
  ldflags = [ "/SUBSYSTEM:WINDOWS$subsystem_version_suffix" ]
}

# Incremental linking ---------------------------------------------------------

incremental_linking_on_switch = [ "/INCREMENTAL" ]
incremental_linking_off_switch = [ "/INCREMENTAL:NO" ]
if (is_debug) {
  default_incremental_linking_switch = incremental_linking_on_switch
} else {
  default_incremental_linking_switch = incremental_linking_off_switch
}

# Applies incremental linking or not depending on the current configuration.
config("default_incremental_linking") {
  ldflags = default_incremental_linking_switch
}

# Explicitly on or off incremental linking
config("incremental_linking") {
  ldflags = incremental_linking_on_switch
}
config("no_incremental_linking") {
  ldflags = incremental_linking_off_switch
}

# Some large modules can't handle incremental linking in some situations. This
# config should be applied to large modules to turn off incremental linking
# when it won't work.
config("default_large_module_incremental_linking") {
  if (current_cpu == "x86" || !is_component_build) {
    # When symbols are on, things get so large that the tools fail due to the
    # size of the .ilk files.
    ldflags = incremental_linking_off_switch
  } else {
    # Otherwise just do the default incremental linking for this build type.
    ldflags = default_incremental_linking_switch
  }
}

# Character set ---------------------------------------------------------------

# Not including this config means "ansi" (8-bit system codepage).
config("unicode") {
  defines = [
    "_UNICODE",
    "UNICODE",
  ]
}

# Lean and mean ---------------------------------------------------------------

# Some third party code might not compile with WIN32_LEAN_AND_MEAN so we have
# to have a separate config for it. Remove this config from your target to
# get the "bloaty and accommodating" version of windows.h.
config("lean_and_mean") {
  defines = [ "WIN32_LEAN_AND_MEAN" ]
}

# Nominmax --------------------------------------------------------------------

# Some third party code defines NOMINMAX before including windows.h, which
# then causes warnings when it's been previously defined on the command line.
# For such targets, this config can be removed.

config("nominmax") {
  defines = [ "NOMINMAX" ]
}

# Relative paths --------------------------------------------------------------

config("relative_paths") {
  # Make builds independent of absolute file path.  The file names
  # embedded in debugging information will be expressed as relative to
  # the build directory, e.g. "../.." for an "out/subdir" under //.
  # This is consistent with the file names in __FILE__ expansions
  # (e.g. in assertion messages), which the compiler doesn't provide a
  # way to remap.  That way source file names in logging and
  # symbolization can all be treated the same way.  This won't go well
  # if root_build_dir is not a subdirectory //, but there isn't a better
  # option to keep all source file name references uniformly relative to
  # a single root.
  if (use_rbe) {
    absolute_path = "/b/f/w/"
  } else {
    absolute_path = rebase_path("//")
  }
  relative_path = ""
  cflags = [
    # This makes sure that debug information uses relative paths.
    "-fdebug-prefix-map=$absolute_path=$relative_path",

    # Remove absolute paths from the debug information.
    "-fdebug-compilation-dir=",
    "-fcoverage-compilation-dir=",

    # This makes sure that include directories in the toolchain are
    # represented as relative to the build directory (because that's how
    # we invoke the compiler), rather than absolute.  This can affect
    # __FILE__ expansions (e.g. assertions in system headers).  We
    # normally run a compiler that's someplace within the source tree
    # (//buildtools/...), so its absolute installation path will have a
    # prefix matching absolute_path and hence be mapped to relative_path
    # in the debugging information, so this should actually be
    # superfluous for purposes of the debugging information.
    "-no-canonical-prefixes",
  ]
}

if (is_clang) {
  build_timestamp =
      exec_script("//tools/make_coff_timestamp.py", [], "trim string")
}

config("deterministic_builds") {
  if (is_clang) {
    # /Brepro lets the compiler not write the mtime field in the .obj output.
    # link.exe /incremental relies on this field to work correctly, but lld
    # never looks at this timestamp, so it's safe to pass this flag with
    # lld and get more deterministic compiler output in return.
    # In LTO builds, the compiler doesn't write .obj files containing mtimes,
    # so /Brepro is ignored there.
    cflags = [ "/Brepro" ]

    # lld defaults to writing the current time in the pe/coff header.
    # For build reproducibility, pass an explicit timestamp. See
    # build/compute_build_timestamp.py for how the timestamp is chosen.
    # (link.exe also writes the current time, but it doesn't have a flag to
    # override that behavior.)
    ldflags = [ "/TIMESTAMP:" + build_timestamp ]

    # Use a fake fixed base directory for paths in the pdb to make the pdb
    # output fully deterministic and independent of the build directory.
    ldflags += [ "/PDBSourcePath:o:\fake\prefix" ]
  }
}
