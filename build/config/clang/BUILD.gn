# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Enables some extra Clang-specific warnings. Some third-party code won't
# compile with these so may want to remove this config.
config("extra_warnings") {
  cflags = [
    "-Wheader-hygiene",

    # Warns when a const char[] is converted to bool.
    "-Wstring-conversion",
  ]
}
