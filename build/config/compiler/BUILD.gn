# Copyright (c) 2013 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

declare_args() {
  # The optimization level to use for debug builds.
  if (is_android) {
    # On Android we kind of optimize some things that don't affect debugging
    # much even when optimization is disabled to get the binary size down.
    debug_optimization_level = "s"
  } else {
    debug_optimization_level = "2"
  }
}

import("//build/config/sysroot.gni")

import("//build/config/android/config.gni")
if (current_cpu == "arm") {
  import("//build/config/arm.gni")
}
if (is_win) {
  import("//build/config/win/visual_studio_version.gni")
}

import("//build/config/c++/c++.gni")
import("//build/toolchain/ccache.gni")

# default_include_dirs ---------------------------------------------------------
#
# This is a separate config so that third_party code (which would not use the
# source root and might have conflicting versions of some headers) can remove
# this and specify their own include paths.
config("default_include_dirs") {
  include_dirs = [
    "//",
    root_gen_dir,
  ]
}

if (!is_win) {
  using_sanitizer =
      is_asan || is_hwasan || is_lsan || is_msan || is_tsan || is_ubsan
}

# compiler ---------------------------------------------------------------------
#
# Base compiler configuration.
#
# See also "runtime_library" below for related stuff and a discussion about
# where stuff should go. Put warning related stuff in the "warnings" config.

config("compiler") {
  asmflags = []
  cflags = []
  cflags_c = []
  cflags_cc = []
  cflags_objcc = []
  ldflags = []
  defines = []

  # In general, Windows is totally different, but all the other builds share
  # some common GCC configuration. This section sets up Windows and the common
  # GCC flags, and then we handle the other non-Windows platforms specifically
  # below.
  if (is_win) {
    # Windows compiler flags setup.
    # -----------------------------
    cflags += [
      "/Gy",  # Enable function-level linking.
      "/GS",  # Enable buffer security checking.
      "/FS",  # Preserve previous PDB behavior.
    ]

    if (is_clang) {
      if (current_cpu == "x86") {
        cflags += [ "-m32" ]
      } else if (current_cpu == "x64") {
        cflags += [ "-m64" ]
      } else if (current_cpu == "arm64") {
        cflags += [ "--target=arm64-windows" ]
      } else {
        assert(false, "Unknown current_cpu: $current_cpu")
      }
    }
  } else {
    # Common GCC compiler flags setup.
    # --------------------------------
    common_flags = [
      # Not exporting C++ inline functions can generally be applied anywhere
      # so we do so here. Normal function visibility is controlled by
      # //build/config/gcc:symbol_visibility_hidden.
      "-fvisibility-inlines-hidden",
    ]
    if (!is_product) {
      common_flags += [
        # We need the frame pointer for CPU and heap profiling.
        "-fno-omit-frame-pointer",
      ]
    }
    cflags_cc += common_flags
    cflags_objcc += common_flags

    # Linker warnings.
    if (current_cpu != "arm" && !is_mac && !is_ios) {
      # TODO(jochen): Enable this on ChromeOS on arm. http://crbug.com/356580
      ldflags += [ "-Wl,--fatal-warnings" ]
    }

    # Enable mitigations for Cortex-A53 Erratum #843419 bug.
    if (current_cpu == "arm64" && is_clang && !is_mac && !is_ios) {
      ldflags += [ "-Wl,--fix-cortex-a53-843419" ]
    }

    # Common options for AddressSanitizer, LeakSanitizer, ThreadSanitizer and
    # MemorySanitizer
    if (is_asan) {
      cflags += [ "-fsanitize=address" ]
      ldflags += [ "-fsanitize=address" ]
      if (is_mac) {
        ldflags += [
          "-rpath",
          "buildtools/mac-$host_cpu/clang/lib/clang/21/lib/darwin",
        ]
      }
    }
    if (is_hwasan && is_android && current_cpu == "arm64") {
      cflags += [ "-fsanitize=hwaddress" ]
      ldflags += [ "-fsanitize=hwaddress" ]
    }
    if (is_lsan) {
      cflags += [ "-fsanitize=leak" ]
      ldflags += [ "-fsanitize=leak" ]
    }
    if (is_msan) {
      cflags += [ "-fsanitize=memory" ]
      ldflags += [ "-fsanitize=memory" ]
    }
    if (is_tsan) {
      cflags += [ "-fsanitize=thread" ]
      ldflags += [ "-fsanitize=thread" ]
    }
    if (is_ubsan) {
      cflags += [
        "-fsanitize=undefined",
        "-fsanitize=float-divide-by-zero",
        "-fsanitize=float-cast-overflow",
      ]
      ldflags += [
        "-fsanitize=undefined",
        "-fsanitize=float-divide-by-zero",
        "-fsanitize=float-cast-overflow",
      ]
    }

    if (use_custom_libcxx) {
      cflags_cc += [ "-nostdinc++" ]
      include_dirs = [
        "//buildtools/third_party/libc++/trunk/include",
        "//buildtools/third_party/libc++abi/trunk/include",
      ]
    }
  }

  if (is_clang && is_debug) {
    # Allow comparing the address of references and 'this' against 0
    # in debug builds. Technically, these can never be null in
    # well-defined C/C++ and Clang can optimize such checks away in
    # release builds, but they may be used in asserts in debug builds.
    extra_flags = [
      "-Wno-undefined-bool-conversion",
      "-Wno-tautological-undefined-compare",
    ]
    cflags_cc += extra_flags
    cflags_objcc += extra_flags
  }

  # Mac-specific compiler flags setup.
  # ----------------------------------
  if (is_mac || is_ios) {
    # These flags are shared between the C compiler and linker.
    common_mac_flags = []

    # CPU architecture.
    if (current_cpu == "x64") {
      common_mac_flags += [
        "-arch",
        "x86_64",
      ]
    } else if (current_cpu == "x86") {
      common_mac_flags += [
        "-arch",
        "i386",
      ]
    } else if (current_cpu == "arm") {
      common_mac_flags += [
        "-arch",
        "armv7",
      ]
    } else if (current_cpu == "arm64") {
      common_mac_flags += [
        "-arch",
        "arm64",
      ]
    }

    cflags += common_mac_flags

    # Without this, the constructors and destructors of a C++ object inside
    # an Objective C struct won't be called, which is very bad.
    cflags_objcc += [ "-fobjc-call-cxx-cdtors" ]

    cflags_c += [ "-std=c99" ]

    ldflags += common_mac_flags
  } else if (is_posix) {
    # CPU architecture. We may or may not be doing a cross compile now, so for
    # simplicity we always explicitly set the architecture.
    if (current_cpu == "x64") {
      cflags += [
        "-m64",
        "-march=x86-64",
        "-msse2",
      ]
      ldflags += [ "-m64" ]
    } else if (current_cpu == "x86") {
      cflags += [
        "-m32",
        "-msse2",
        "-mfpmath=sse",
      ]
      ldflags += [ "-m32" ]
      if (is_clang) {
        cflags += [
          # Else building libyuv gives clang's register allocator issues,
          # see llvm.org/PR15798 / crbug.com/233709
          "-mno-omit-leaf-frame-pointer",
        ]
      }
    } else if (current_cpu == "arm") {
      cflags += [
        "-march=$arm_arch",
        "-mfpu=$arm_fpu",
        "-mfloat-abi=$arm_float_abi",
      ]
      if (arm_tune != "") {
        cflags += [ "-mtune=$arm_tune" ]
      }
    }
  }

  # Linux/Android common flags setup.
  # ---------------------------------
  if (is_linux || is_android) {
    ldflags += [
      "-Wl,-z,noexecstack",
      "-Wl,-z,now",
      "-Wl,-z,relro",
      "-Wl,--build-id=sha1",
    ]
  }

  if (is_android || is_linux || is_mac || is_ios || is_fuchsia) {
    if (is_shared_library) {
      cflags += [ "-fPIC" ]
      ldflags += [ "-fPIC" ]
    } else {
      cflags += [ "-fPIE" ]
      ldflags += [ "-fPIE" ]
    }
  }

  # Linux-specific compiler flags setup.
  # ------------------------------------
  if (is_linux) {
    if (is_clang) {
      if (dart_sysroot == "alpine") {
        # alpine linux target names can be found at:
        # https://pkgs.alpinelinux.org/contents?file=stdc%2B%2B.h&name=libstdc%2B%2B-dev
        if (current_cpu == "arm") {
          cflags += [ "--target=armv7-alpine-linux-musleabihf" ]
          ldflags += [ "--target=armv7-alpine-linux-musleabihf" ]
        } else if (current_cpu == "arm64") {
          cflags += [ "--target=aarch64-alpine-linux-musl" ]
          ldflags += [ "--target=aarch64-alpine-linux-musl" ]
        } else if (current_cpu == "riscv32") {
          cflags += [ "--target=riscv32-alpine-linux-musl" ]
          ldflags += [ "--target=riscv32-alpine-linux-musl" ]
        } else if (current_cpu == "riscv64") {
          cflags += [ "--target=riscv64-alpine-linux-musl" ]
          ldflags += [ "--target=riscv64-alpine-linux-musl" ]
        } else if (current_cpu == "x64") {
          cflags += [ "--target=x86_64-alpine-linux-musl" ]
          ldflags += [ "--target=x86_64-alpine-linux-musl" ]
        } else if (current_cpu == "x86") {
          cflags += [ "--target=i586-alpine-linux-musl" ]
          ldflags += [ "--target=i586-alpine-linux-musl" ]
        }
      } else {
        if (current_cpu == "arm") {
          cflags += [ "--target=armv7-linux-gnueabihf" ]
          ldflags += [ "--target=armv7-linux-gnueabihf" ]
        } else if (current_cpu == "arm64") {
          cflags += [ "--target=aarch64-linux-gnu" ]
          ldflags += [ "--target=aarch64-linux-gnu" ]
        } else if (current_cpu == "riscv32") {
          cflags += [ "--target=riscv32-linux-gnu" ]
          ldflags += [ "--target=riscv32-linux-gnu" ]
        } else if (current_cpu == "riscv64") {
          cflags += [ "--target=riscv64-linux-gnu" ]
          ldflags += [ "--target=riscv64-linux-gnu" ]
        } else if (current_cpu == "x64") {
          cflags += [ "--target=x86_64-linux-gnu" ]
          ldflags += [ "--target=x86_64-linux-gnu" ]
        } else if (current_cpu == "x86") {
          cflags += [ "--target=i386-linux-gnu" ]
          ldflags += [ "--target=i386-linux-gnu" ]
        }
      }
    }
  }

  # Clang-specific compiler flags setup.
  # ------------------------------------
  if (is_clang) {
    cflags += [ "-fcolor-diagnostics" ]
  }

  # C++ standard compiler flags setup.
  # ---------------------------
  if (is_win) {
    cc_std = [
      # This option fixes the value of the __cplusplus macro when using MSVC.
      "/Zc:__cplusplus",
      "/std:c++20",
    ]
  } else {
    cc_std = [ "-std=c++20" ]
  }
  cflags_cc += cc_std
  cflags_objcc += cc_std

  # Android-specific flags setup.
  # -----------------------------
  if (is_android) {
    cflags += [
      "-ffunction-sections",
      "-funwind-tables",
      "-fno-short-enums",
    ]
    if (is_asan) {
      # Android build relies on -Wl,--gc-sections removing unreachable code.
      # ASan instrumentation for globals inhibits this and results in a library
      # with unresolvable relocations.
      # TODO(eugenis): find a way to reenable this.
      cflags += [ "-mllvm -asan-globals=0" ]
    }

    defines += [ "ANDROID" ]

    # The NDK has these things, but doesn't define the constants
    # to say that it does. Define them here instead.
    defines += [ "HAVE_SYS_UIO_H" ]

    ldflags += [
      # Don't allow visible symbols from libgcc or libc++ to be
      # re-exported.
      "-Wl,--exclude-libs=libc++_static.a",

      # Currently defaults to 4k, but Android will be moving to 16k page size,
      # and for future-proofing, 64k boundaries will be required.
      "-Wl,-z,max-page-size=65536",
    ]

    if (is_clang) {
      if (current_cpu == "arm") {
        cflags += [ "--target=arm-linux-androideabi${android_api_level}" ]
        ldflags += [ "--target=arm-linux-androideabi${android_api_level}" ]
      } else if (current_cpu == "arm64") {
        cflags += [ "--target=aarch64-linux-android${android_api_level}" ]
        ldflags += [ "--target=aarch64-linux-android${android_api_level}" ]
      } else if (current_cpu == "x86") {
        cflags += [ "--target=i686-linux-androideabi${android_api_level}" ]
        ldflags += [ "--target=i686-linux-androideabi${android_api_level}" ]
      } else if (current_cpu == "x64") {
        cflags += [ "--target=x86_64-linux-androideabi${android_api_level}" ]
        ldflags += [ "--target=x86_64-linux-androideabi${android_api_level}" ]
      } else if (current_cpu == "riscv64") {
        cflags += [ "--target=riscv64-linux-android${android_api_level}" ]
        ldflags += [ "--target=riscv64-linux-android${android_api_level}" ]
      }
    }
  }

  # We want to force a recompile and relink of the world whenever our toolchain
  # changes since artifacts from an older version of the toolchain may or may
  # not be compatible with newer ones. To achieve this, we insert a synthetic
  # define into the compile line.
  if (is_clang && (is_linux || is_mac || is_ios) && dart_sysroot != "alpine") {
    if (is_linux && host_cpu == "arm64") {
      toolchain_stamp_file =
          "//buildtools/linux-arm64/clang/.versions/clang.cipd_version"
    } else if (is_linux) {
      toolchain_stamp_file =
          "//buildtools/linux-x64/clang/.versions/clang.cipd_version"
    } else if ((is_mac || is_ios) && host_cpu == "arm64") {
      toolchain_stamp_file =
          "//buildtools/mac-arm64/clang/.versions/clang.cipd_version"
    } else if (is_mac || is_ios) {
      toolchain_stamp_file =
          "//buildtools/mac-x64/clang/.versions/clang.cipd_version"
    }
    toolchain_cipd_version = read_file(toolchain_stamp_file, "json")
    defines = [ "TOOLCHAIN_VERSION=${toolchain_cipd_version.instance_id}" ]

    if (is_linux) {
      if (current_cpu == "riscv64") {
        sysroot_stamp_file =
            "//buildtools/sysroot/focal/.versions/sysroot.cipd_version"
      } else {
        sysroot_stamp_file =
            "//buildtools/sysroot/linux/.versions/sysroot.cipd_version"
      }
      sysroot_cipd_version = read_file(sysroot_stamp_file, "json")
      defines += [ "SYSROOT_VERSION=${sysroot_cipd_version.instance_id}" ]
    }
  }

  # Assign any flags set for the C compiler to asmflags so that they are sent
  # to the assembler. The Windows assembler takes different types of flags
  # so only do so for posix platforms.
  if (is_posix) {
    asmflags += cflags
    asmflags += cflags_c
  }
}

config("cxx_version_default") {
  if (is_win) {
    cflags_c = [ "/std:c17" ]
    cc_std = [ "/std:c++20" ]
  } else {
    cflags_c = [ "-std=c17" ]
    cc_std = [ "-std=c++20" ]
  }
  cflags_cc = cc_std
  cflags_objcc = cc_std
}

config("cxx_version_11") {
  if (is_win) {
    cflags_c = [ "/std:c11" ]
    cc_std = [ "/std:c++11" ]
  } else {
    cflags_c = [ "-std=c11" ]
    cc_std = [ "-std=c++11" ]
  }
  cflags_cc = cc_std
  cflags_objcc = cc_std
}

config("cxx_version_14") {
  if (is_win) {
    cflags_c = [ "/std:c14" ]
    cc_std = [ "/std:c++14" ]
  } else {
    cflags_c = [ "-std=c14" ]
    cc_std = [ "-std=c++14" ]
  }
  cflags_cc = cc_std
  cflags_objcc = cc_std
}

config("cxx_version_17") {
  if (is_win) {
    cflags_c = [ "/std:c17" ]
    cc_std = [ "/std:c++17" ]
  } else {
    cflags_c = [ "-std=c17" ]
    cc_std = [ "-std=c++17" ]
  }
  cflags_cc = cc_std
  cflags_objcc = cc_std
}

config("cxx_version_20") {
  if (is_win) {
    cflags_c = [ "/std:c17" ]
    cc_std = [ "/std:c++20" ]
  } else {
    cflags_c = [ "-std=c17" ]
    cc_std = [ "-std=c++20" ]
  }
  cflags_cc = cc_std
  cflags_objcc = cc_std
}

# This is separate from :compiler_codegen (and not even a sub-config there)
# so that some targets can remove it from the list with:
#   configs -= [ "//build/config/compiler:clang_stackrealign" ]
# See https://crbug.com/556393 for details of where it must be avoided.
config("clang_stackrealign") {
  if (is_clang && current_cpu == "x86" && !is_nacl && !is_win) {
    cflags = [
      # Align the stack on 16-byte boundaries, http://crbug.com/418554.
      "-mstack-alignment=16",
      "-mstackrealign",
    ]
  }
}

config("compiler_arm_fpu") {
  if (current_cpu == "arm") {
    cflags = [ "-mfpu=$arm_fpu" ]
  }
}

config("compiler_arm_thumb") {
  if (current_cpu == "arm") {
    if (arm_use_thumb) {
      cflags = [ "-mthumb" ]
      if (is_android && !is_clang) {  # Clang doesn't support this option.
        cflags += [ "-mthumb-interwork" ]
      }
      asmflags = cflags
    }
  }
}

# runtime_library -------------------------------------------------------------
#
# Sets the runtime library and associated options.
#
# How do you determine what should go in here vs. "compiler" above? Consider if
# a target might choose to use a different runtime library (ignore for a moment
# if this is possible or reasonable on your system). If such a target would
# want to change or remove your option, put it in the runtime_library config.
# If a target wants the option regardless, put it in the compiler config.

config("runtime_library") {
  cflags = []
  cflags_cc = []
  cflags_objcc = []
  defines = []
  ldflags = []
  lib_dirs = []
  libs = []

  # Static CRT.
  if (is_win) {
    if (is_debug) {
      cflags += [ "/MTd" ]
    } else {
      cflags += [ "/MT" ]
    }
    defines += [
      "__STD_C",
      "_CRT_RAND_S",
      "_CRT_SECURE_NO_DEPRECATE",
      "_HAS_EXCEPTIONS=0",
      "_SCL_SECURE_NO_DEPRECATE",
    ]
  }

  if (use_flutter_cxx) {
    cflags_cc += [ "-nostdinc++" ]
    cflags_objcc += [ "-nostdinc++" ]
    ldflags += [ "-nostdlib++" ]
    include_dirs = [
      "//third_party/libcxx/include",
      "//third_party/libcxxabi/include",
    ]
  }

  if (is_linux) {
    libs += [
      "dl",
      "pthread",
    ]
  } else if (is_android) {
    # Android standard library setup.

    ldflags += [
      "-Wl,--warn-shared-textrel",
      "-static-libstdc++",
    ]

    libs += [
      "c",
      "dl",
      "m",
    ]
  }
}

# default_warning_flags collects all warning flags that are used by default.
# This is in a variable instead of a config so that it can be used in
# both chromium_code and no_chromium_code.  This way these flags are guaranteed
# to appear on the compile command line after -Wall.

default_warning_flags = []
default_warning_flags_cc = []
if (is_win) {
  if (current_cpu != "x86") {
    default_warning_flags += [ "/WX" ]  # Treat warnings as errors.
  }

  if (is_clang) {
    default_warning_flags += [
      "-Wno-deprecated-declarations",  # crashpad
      "-Wno-ignored-pragma-optimize",  # icu, double-conversion
      "-Wno-macro-redefined",
      "-Wno-microsoft-cast",
      "-Wno-microsoft-unqualified-friend",
      "-Wno-unknown-argument",  # icu
      "-Wno-unused-value",  # crashpad
      "-Wno-deprecated-non-prototype",  # zlib
    ]
  } else {
    default_warning_flags += [
      # Permanent.
      "/wd4091",  # typedef warning from dbghelp.h
      "/wd4722",  # destructor never returns

      # Investigate.
      "/wd4312",  # int to pointer of greater size conversion.
      "/wd4838",  # Narrowing conversion required.
      "/wd4172",  # Returning address of local.
      "/wd4005",  # Redefinition of macros for PRId64 etc.
      "/wd4311",  # Pointer truncation from PVOID to DWORD.
      "/wd4477",  # Format string requires wchar_t*
    ]
  }
} else {
  # Common GCC warning setup.
  default_warning_flags += [
    # Enables.
    "-Wendif-labels",  # Weird old-style text after an #endif.

    # Disables.
    "-Wno-missing-field-initializers",  # "struct foo f = {0};"
    "-Wno-unused-parameter",  # Unused function parameters.
  ]

  if (is_clang) {
    default_warning_flags += [
      "-Wno-tautological-constant-compare",
      "-Wno-unused-but-set-variable",  # icu
      "-Wno-deprecated-non-prototype",  # zlib
    ]
  } else {
    default_warning_flags += [
      "-Wno-ignored-qualifiers",  # Warnings in BoringSSL headers
    ]
  }

  if (is_mac || is_ios) {
    # TODO(abarth): Re-enable once https://github.com/domokit/mojo/issues/728
    #               is fixed.
    # default_warning_flags += [ "-Wnewline-eof" ]

    # When compiling Objective-C, warns if a method is used whose
    # availability is newer than the deployment target. This is not
    # required when compiling Chrome for iOS.
    default_warning_flags += [ "-Wpartial-availability" ]
  }

  # Suppress warnings about ABI changes on ARM (Clang doesn't give this
  # warning).
  if (current_cpu == "arm" && !is_clang) {
    default_warning_flags += [ "-Wno-psabi" ]
  }

  # The Raspberry Pi 1 toolchain enables this warning, but Dart doesn't build
  # cleanly with it.
  if (is_linux && !is_clang && current_cpu == "arm" && arm_version == 6) {
    default_warning_flags += [ "-Wno-type-limits" ]
  }

  if (is_android) {
    # Disable any additional warnings enabled by the Android build system but
    # which chromium does not build cleanly with (when treating warning as
    # errors).
    default_warning_flags += [
      "-Wno-extra",
      "-Wno-ignored-qualifiers",
      "-Wno-type-limits",
    ]
    default_warning_flags_cc += [
      # Other things unrelated to -Wextra:
      "-Wno-non-virtual-dtor",
      "-Wno-sign-promo",
    ]
  }
}

# chromium_code ---------------------------------------------------------------
#
# Toggles between higher and lower warnings for code that is (or isn't)
# part of Chromium.

config("chromium_code") {
  if (is_win) {
    # TODO(zra): Enable higher warning levels.
    # cflags = [ "/W4" ]  # Warning level 4.
    cflags = []
  } else {
    cflags = [
      "-Wall",
      "-Wextra",
    ]

    if (dart_sysroot != "alpine") {
      cflags += [ "-Werror" ]
    }

    defines = []
    if (!using_sanitizer && !is_clang) {
      # _FORTIFY_SOURCE isn't really supported by Clang now, see
      # http://llvm.org/bugs/show_bug.cgi?id=16821.
      # It seems to work fine with Ubuntu 12 headers though, so use it in
      # official builds.
      #
      # Non-chromium code is not guaranteed to compile cleanly with
      # _FORTIFY_SOURCE. Also, fortified build may fail when optimizations are
      # disabled, so only do that for Release build.
      defines += [ "_FORTIFY_SOURCE=2" ]
    }
  }
  cflags += default_warning_flags
  cflags_cc = default_warning_flags_cc
}
config("no_chromium_code") {
  cflags = []
  cflags_cc = []
  defines = []

  if (is_win) {
    defines += [
      "_CRT_NONSTDC_NO_WARNINGS",
      "_CRT_NONSTDC_NO_DEPRECATE",
    ]
  }

  cflags += default_warning_flags
  cflags_cc += default_warning_flags_cc
}

# rtti ------------------------------------------------------------------------
#
# Allows turning Run-Time Type Identification on or off.

config("rtti") {
  if (is_win) {
    cflags_cc = [ "/GR" ]
  }
}
config("no_rtti") {
  if (is_win) {
    cflags_cc = [ "/GR-" ]
  } else {
    rtti_flags = [ "-fno-rtti" ]
    cflags_cc = rtti_flags
    cflags_objcc = rtti_flags
  }
}

config("enable_exceptions") {
  if (is_win) {
    cflags_cc = [ "/EHsc" ]
    defines = [ "_HAS_EXCEPTIONS=1" ]
  } else if (is_clang) {
    cflags_cc = [ "-fexceptions" ]
  }
}

# Optimization -----------------------------------------------------------------
#
# Note that BUILDCONFIG.gn sets up a variable "default_optimization_config"
# which it will assign to the config it implicitly applies to every target. If
# you want to override the optimization level for your target, remove this
# config (which will expand differently for debug or release builds), and then
# add back the one you want to override it with:
#
#   configs -= default_optimization_config
#   configs += [ ":optimize_max" ]

# Shared settings.
# IMPORTANT: On Windows "/O1" and "/O2" must go before the common flags.
if (is_win) {
  common_optimize_on_cflags = [
    "/Ob2",  # Both explicit and auto inlining.
    "/Oy-",  # Disable omitting frame pointers, must be after /O2.
  ]
  if (!is_asan) {
    common_optimize_on_cflags += [
      # Put data in separate COMDATs. This allows the linker
      # to put bit-identical constants at the same address even if
      # they're unrelated constants, which saves binary size.
      # This optimization can't be used when ASan is enabled because
      # it is not compatible with the ASan ODR checker.
      "/Gw",
    ]
  }
  common_optimize_on_ldflags = [
    # Linker GC.
    "/OPT:REF",

    # Identical code folding to reduce size.
    # Warning: This changes C/C++ semantics of function pointer comparison.
    "/OPT:ICF",
  ]
} else {
  common_optimize_on_cflags = [
    # Don't emit the GCC version ident directives, they just end up in the
    # .comment section taking up binary size.
    "-fno-ident",

    # Put data and code in their own sections, so that unused symbols
    # can be removed at link time with --gc-sections.
    "-fdata-sections",
    "-ffunction-sections",
  ]
  common_optimize_on_ldflags = []

  if (is_android) {
    common_optimize_on_ldflags += [
      # Warn in case of text relocations.
      "-Wl,--warn-shared-textrel",
    ]
  }

  if (is_mac || is_ios) {
    # Mac dead code stripping requires symbols.
    common_optimize_on_ldflags += [ "-Wl,-dead_strip" ]
  } else {
    # Non-Mac Posix linker flags.
    common_optimize_on_ldflags += [
      # Specifically tell the linker to perform optimizations.
      # See http://lwn.net/Articles/192624/ .
      "-Wl,-O2",
      "-Wl,--gc-sections",
    ]

    if (is_clang && !using_sanitizer) {
      # Identical code folding to reduce size.
      # Warning: This changes C/C++ semantics of function pointer comparison.
      common_optimize_on_ldflags += [ "-Wl,--icf=all" ]
    }

    if (!using_sanitizer) {
      # Functions interposed by the sanitizers can make ld think
      # that some libraries aren't needed when they actually are,
      # http://crbug.com/234010. As workaround, disable --as-needed.
      common_optimize_on_ldflags += [ "-Wl,--as-needed" ]
    }
  }
}

# Default "optimization on" config. On Windows, this favors size over speed.
config("optimize") {
  if (is_win) {
    cflags = [ "/O2" ] + common_optimize_on_cflags + [ "/Oi" ]
  } else {
    cflags = [ "-O2" ] + common_optimize_on_cflags
  }
  ldflags = common_optimize_on_ldflags
}

# Turn off optimizations.
config("no_optimize") {
  if (is_win) {
    # The only difference on windows is that the inlining is less aggressive.
    # (We accept the default level). Otherwise it is very slow.
    if (is_clang && debug_optimization_level != "2") {
      cflags = [
        "-d${debug_optimization_level}",  # Do some optimizations.
        "/Oy-",  # Disable omitting frame pointers, must be after /O2.
      ]
    } else {
      cflags = [
        "/O${debug_optimization_level}",  # Do some optimizations.
        "/Oy-",  # Disable omitting frame pointers, must be after /O2.
      ]
    }
  } else if (is_android) {
    # On Android we kind of optimize some things that don't affect debugging
    # much even when optimization is disabled to get the binary size down.
    cflags = [
      "-O${debug_optimization_level}",
      "-fdata-sections",
      "-ffunction-sections",
    ]
    ldflags = common_optimize_on_ldflags
  } else {
    cflags = [
      "-O${debug_optimization_level}",
      "-fdata-sections",
      "-ffunction-sections",
    ]
  }
}

# These are two named configs that zlib's BUILD.gn expects to exist.
config("default_optimization") {
}

config("optimize_speed") {
}

# Symbols ----------------------------------------------------------------------

config("symbols") {
  if (is_win) {
    cflags = [ "/Zi" ]  # Produce PDB file, no edit and continue.
    ldflags = [ "/DEBUG" ]
  } else {
    cflags = [
      "-g3",
      "-ggdb3",
    ]
  }
}

config("dart_shared_lib") {
  defines = [ "DART_SHARED_LIB" ]
}
