# Copyright 2015 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/linux/pkg_config.gni")

assert(is_linux, "This file should only be referenced on Linux")

# Depend on //build/config/linux/gtk to use GTK.
#
# GN doesn't check visibility for configs so we give this an obviously internal
# name to discourage random targets from accidentally depending on this and
# bypassing the GTK target's visibility.
pkg_config("gtk_internal_config") {
  # Gtk requires gmodule, but it does not list it as a dependency in some
  # misconfigured systems.
  packages = [
    "gmodule-2.0",
    "gtk+-2.0",
    "gthread-2.0",
  ]
}

# Basically no parts of Chrome should depend on GTK. To prevent accidents, the
# parts that explicitly need GTK are whitelisted on this target.
group("gtk") {
  visibility = [
    "//chrome/browser/ui/libgtk2ui",
    "//gpu/gles2_conform_support:gles2_conform_test_windowless",
    "//remoting/host",
    "//remoting/host/it2me:remote_assistance_host",
    "//remoting/host:remoting_me2me_host_static",
  ]
  public_configs = [ ":gtk_internal_config" ]
}

# Depend on "gtkprint" to get this.
pkg_config("gtkprint_internal_config") {
  packages = [ "gtk+-unix-print-2.0" ]
}

group("gtkprint") {
  visibility = [ "//chrome/browser/ui/libgtk2ui" ]
  public_configs = [ ":gtkprint_internal_config" ]
}
