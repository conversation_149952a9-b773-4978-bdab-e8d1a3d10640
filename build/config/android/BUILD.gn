# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/sysroot.gni")

config("sdk") {
  if (sysroot != "") {
    cflags = [ "--sysroot=" + sysroot ]

    # Need to get some linker flags out of the sysroot.
    sysroot_ld_path = rebase_path("//build/config/linux/sysroot_ld_path.py")
    ldflags = [ exec_script(sysroot_ld_path,
                            [
                              rebase_path("//build/linux/sysroot_ld_path.sh"),
                              sysroot,
                            ],
                            "value") ]
  }
}

config("executable_config") {
  cflags = [ "-fPIE" ]
  ldflags = [ "-pie" ]
}
