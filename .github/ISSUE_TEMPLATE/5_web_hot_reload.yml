name: Report an issue related to Dart Web Stateful Hot Reload
description: |
  Create an issue specific to the Stateful Hot Reload feature available via the Dart web development compiler (DDC).
labels: ["area-web", "web-hot-reload", "web-dev-compiler"]
assignees:
 - nshahan
 - biggs0125
body:
  - type: markdown
    attributes:
      value: |
        Begin by checking our [Web Hot Reload - Known Issues](https://github.com/orgs/dart-lang/projects/107/views/1) project to ensure this is not a known issue.

        If a similar issue has not been filed yet, use this template to file a new issue with the correct labels.

        If the issue you are experiencing presents itself in Flutter, please file an issue here in the Dart repository. We will forward any Flutter-specific issues to the Flutter repository as needed.

        Note: Hot reload rejections are expected errors that occur when a change is made that prevents the compiler from generating valid hot reload code. If you encounter such a rejection you must either undo the change to reload or perform a full hot restart.

  - type: textarea
    attributes:
      label: Steps to reproduce
      description: Please tell us how to reproduce the problem you are running into.
      placeholder: |
        1. ...
        2. ...
        3. ...
  - type: textarea
    attributes:
      label: Code sample
      description: |
        If possible please include the code involved in the error or a similar minimal reproduction of the issue. Please also include any previous versions of the code prior to hot reloading that may have contributed to the issue.
  - type: textarea
    attributes:
      label: Flutter version
      description: Please include the version of Flutter this issue was discovered in. If you are using a published SDK (e.g. from the main channel) please include the verison emitted by `flutter --version`. If you're using a locally built SDK, please include the git commit of your local Flutter checkout.
  - type: markdown
    attributes:
      value: |
        Thank you for helping improve hot reload on the web!
