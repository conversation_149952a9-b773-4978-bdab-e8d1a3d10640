---
name: Report a general issue.
about: Report a bug or file a feature request.
---

Thank you for taking the time to file an issue! This tracker is for issues related to the Dart analyzer and linter, the Dart core libraries (`dart:async`, `dart:io`, etc.), the Dart native and web compilers, and the Dart VM.

When reporting an issue, please include:

- The Dart version and tooling diagnostic info (`dart info`)
- Whether you are using Windows, macOS, or Linux (if applicable)
- Whether you are using Chrome, Safari, Firefox, Edge (if applicable)

Other pieces of the Dart ecosystem are maintained elsewhere; please file issues in their respective repositories:

- Flutter: https://github.com/flutter/flutter
- Dart language: https://github.com/dart-lang/language
- Dart website: https://github.com/dart-lang/site-www
- `dart format`: https://github.com/dart-lang/dart_style
- `dart pub`: https://github.com/dart-lang/pub
- `dart test`: https://github.com/dart-lang/test
- Dart & Flutter DevTools: https://github.com/flutter/devtools

If you have a question, instead of using this issue tracker, please refer to the community resources at: https://dart.dev/community#join-the-conversation.
