// Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

// ignore_for_file: unused_field

typedef State = _Class;

class _Class {
  final int index;
  const _Class(this.index);

  static const start = _Class(0);
  static const a1 = _Class(1);
  static const a2 = _Class(2);
  static const a3 = _Class(3);
  static const a4 = _Class(4);
  static const a5 = _Class(5);
  static const a6 = _Class(6);
  static const a7 = _Class(7);

  static const error = _Class(8);

  static const extended1 = _Class(9);
  static const extended2 = _Class(10);
  static const extended3 = _Class(11);
  static const extended4 = _Class(12);

  static const two3 = _Class(13);
  static const two4 = _Class(14);
  static const two5 = _Class(15);
  static const two6 = _Class(16);
  static const two7 = _Class(17);

  static const three4 = _Class(18);
  static const three5 = _Class(19);
  static const three6 = _Class(20);
  static const three7 = _Class(21);

  static const four5 = _Class(22);
  static const four6 = _Class(23);
  static const four7 = _Class(24);

  // First additional byte of 3.
  static const first0 = _Class(25);
  static const first1 = _Class(26);
  static const first2 = _Class(27);
  static const first3 = _Class(28);
  static const first4 = _Class(29);
  static const first5 = _Class(30);
  static const first6 = _Class(31);
  static const first7 = _Class(32);

  // Second last additional byte.
  static const prev0 = _Class(33);
  static const prev1 = _Class(34);
  static const prev2 = _Class(35);
  static const prev3 = _Class(36);
  static const prev4 = _Class(37);
  static const prev5 = _Class(38);
  static const prev6 = _Class(39);
  static const prev7 = _Class(40);

  // Last additional byte.
  static const last0 = _Class(41);
  static const last1 = _Class(42);
  static const last2 = _Class(43);
  static const last3 = _Class(44);
  static const last4 = _Class(45);
  static const last5 = _Class(46);
  static const last6 = _Class(47);
  static const last7 = _Class(48);
}
