// Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

class State {
  static const int start = 0;
  static const int a1 = 1;
  static const int a2 = 2;
  static const int a3 = 3;
  static const int a4 = 4;
  static const int a5 = 5;
  static const int a6 = 6;
  static const int a7 = 7;

  static const int error = 8;

  static const int extended1 = 9;
  static const int extended2 = 10;
  static const int extended3 = 11;
  static const int extended4 = 12;

  static const int two3 = 13;
  static const int two4 = 14;
  static const int two5 = 15;
  static const int two6 = 16;
  static const int two7 = 17;

  static const int three4 = 18;
  static const int three5 = 19;
  static const int three6 = 20;
  static const int three7 = 21;

  static const int four5 = 22;
  static const int four6 = 23;
  static const int four7 = 24;

  // First additional byte of 3.
  static const int first0 = 25;
  static const int first1 = 26;
  static const int first2 = 27;
  static const int first3 = 28;
  static const int first4 = 29;
  static const int first5 = 30;
  static const int first6 = 31;
  static const int first7 = 32;

  // Second last additional byte.
  static const int prev0 = 33;
  static const int prev1 = 34;
  static const int prev2 = 35;
  static const int prev3 = 36;
  static const int prev4 = 37;
  static const int prev5 = 38;
  static const int prev6 = 39;
  static const int prev7 = 40;

  // Last additional byte.
  static const int last0 = 41;
  static const int last1 = 42;
  static const int last2 = 43;
  static const int last3 = 44;
  static const int last4 = 45;
  static const int last5 = 46;
  static const int last6 = 47;
  static const int last7 = 48;
}
