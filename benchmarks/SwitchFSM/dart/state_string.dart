// Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

// ignore_for_file: unused_field

typedef State = _Strings;

class _Strings {
  static const String start = 'start';
  static const String a1 = 'a1';
  static const String a2 = 'a2';
  static const String a3 = 'a3';
  static const String a4 = 'a4';
  static const String a5 = 'a5';
  static const String a6 = 'a6';
  static const String a7 = 'a7';

  static const String error = 'error';

  static const String extended1 = 'extended1';
  static const String extended2 = 'extended2';
  static const String extended3 = 'extended3';
  static const String extended4 = 'extended4';

  static const String two3 = 'two3';
  static const String two4 = 'two4';
  static const String two5 = 'two5';
  static const String two6 = 'two6';
  static const String two7 = 'two7';

  static const String three4 = 'three4';
  static const String three5 = 'three5';
  static const String three6 = 'three6';
  static const String three7 = 'three7';

  static const String four5 = 'four5';
  static const String four6 = 'four6';
  static const String four7 = 'four7';

  // First additional byte of 3.
  static const String first0 = 'first0';
  static const String first1 = 'first1';
  static const String first2 = 'first2';
  static const String first3 = 'first3';
  static const String first4 = 'first4';
  static const String first5 = 'first5';
  static const String first6 = 'first6';
  static const String first7 = 'first7';

  // Second last additional byte.
  static const String prev0 = 'prev0';
  static const String prev1 = 'prev1';
  static const String prev2 = 'prev2';
  static const String prev3 = 'prev3';
  static const String prev4 = 'prev4';
  static const String prev5 = 'prev5';
  static const String prev6 = 'prev6';
  static const String prev7 = 'prev7';

  // Last additional byte.
  static const String last0 = 'last0';
  static const String last1 = 'last1';
  static const String last2 = 'last2';
  static const String last3 = 'last3';
  static const String last4 = 'last4';
  static const String last5 = 'last5';
  static const String last6 = 'last6';
  static const String last7 = 'last7';
}
