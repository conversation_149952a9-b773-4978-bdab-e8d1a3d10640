// Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

enum State {
  start,
  a1,
  a2,
  a3,
  a4,
  a5,
  a6,
  a7,

  error,

  extended1,
  extended2,
  extended3,
  extended4,

  two3,
  two4,
  two5,
  two6,
  two7,

  three4,
  three5,
  three6,
  three7,

  four5,
  four6,
  four7,

  // First additional byte of 3.
  first0,
  first1,
  first2,
  first3,
  first4,
  first5,
  first6,
  first7,

  // Second last additional byte.
  prev0,
  prev1,
  prev2,
  prev3,
  prev4,
  prev5,
  prev6,
  prev7,

  // Last additional byte.
  last0,
  last1,
  last2,
  last3,
  last4,
  last5,
  last6,
  last7,
}
