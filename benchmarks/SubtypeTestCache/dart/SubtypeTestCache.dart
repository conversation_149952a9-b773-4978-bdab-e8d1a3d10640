// Copyright (c) 2023, the Dart project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.
//
// This benchmark suite measures the overhead of looking up elements of
// SubtypeTestCaches, which are used when a type testing stub cannot determine
// whether a given type is assignable.

import 'package:benchmark_harness/benchmark_harness.dart';

void main() {
  const STC1().report();
  const STC5().report();
  const STC10().report();
  const STC25().report();
  const STC50().report();
  const STC75().report();
  const STC100().report();
  const STC250().report();
  const STC500().report();
  const STC750().report();
  const STC1000().report();
  const STCSame1000().report();
}

class STCBenchmarkBase extends BenchmarkBase {
  final int count;
  const STCBenchmarkBase(String name, this.count) : super(name);

  // Normalize the cost across the benchmarks by number of type tests.
  @override
  void report() => emitter.emit(name, measure() / count);
}

class STC1 extends STCBenchmarkBase {
  const STC1() : super('SubtypeTestCache.STC1', 1);

  @override
  void run() {
    check<int>(instances[0]);
  }
}

class STC5 extends STCBenchmarkBase {
  const STC5() : super('SubtypeTestCache.STC5', 5);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
  }
}

class STC10 extends STCBenchmarkBase {
  const STC10() : super('SubtypeTestCache.STC10', 10);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
  }
}

class STC25 extends STCBenchmarkBase {
  const STC25() : super('SubtypeTestCache.STC25', 25);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
  }
}

class STC50 extends STCBenchmarkBase {
  const STC50() : super('SubtypeTestCache.STC50', 50);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
  }
}

class STC75 extends STCBenchmarkBase {
  const STC75() : super('SubtypeTestCache.STC75', 75);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
  }
}

class STC100 extends STCBenchmarkBase {
  const STC100() : super('SubtypeTestCache.STC100', 100);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
    check<int>(instances[75]);
    check<int>(instances[76]);
    check<int>(instances[77]);
    check<int>(instances[78]);
    check<int>(instances[79]);
    check<int>(instances[80]);
    check<int>(instances[81]);
    check<int>(instances[82]);
    check<int>(instances[83]);
    check<int>(instances[84]);
    check<int>(instances[85]);
    check<int>(instances[86]);
    check<int>(instances[87]);
    check<int>(instances[88]);
    check<int>(instances[89]);
    check<int>(instances[90]);
    check<int>(instances[91]);
    check<int>(instances[92]);
    check<int>(instances[93]);
    check<int>(instances[94]);
    check<int>(instances[95]);
    check<int>(instances[96]);
    check<int>(instances[97]);
    check<int>(instances[98]);
    check<int>(instances[99]);
  }
}

class STC250 extends STCBenchmarkBase {
  const STC250() : super('SubtypeTestCache.STC250', 250);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
    check<int>(instances[75]);
    check<int>(instances[76]);
    check<int>(instances[77]);
    check<int>(instances[78]);
    check<int>(instances[79]);
    check<int>(instances[80]);
    check<int>(instances[81]);
    check<int>(instances[82]);
    check<int>(instances[83]);
    check<int>(instances[84]);
    check<int>(instances[85]);
    check<int>(instances[86]);
    check<int>(instances[87]);
    check<int>(instances[88]);
    check<int>(instances[89]);
    check<int>(instances[90]);
    check<int>(instances[91]);
    check<int>(instances[92]);
    check<int>(instances[93]);
    check<int>(instances[94]);
    check<int>(instances[95]);
    check<int>(instances[96]);
    check<int>(instances[97]);
    check<int>(instances[98]);
    check<int>(instances[99]);
    check<int>(instances[100]);
    check<int>(instances[101]);
    check<int>(instances[102]);
    check<int>(instances[103]);
    check<int>(instances[104]);
    check<int>(instances[105]);
    check<int>(instances[106]);
    check<int>(instances[107]);
    check<int>(instances[108]);
    check<int>(instances[109]);
    check<int>(instances[110]);
    check<int>(instances[111]);
    check<int>(instances[112]);
    check<int>(instances[113]);
    check<int>(instances[114]);
    check<int>(instances[115]);
    check<int>(instances[116]);
    check<int>(instances[117]);
    check<int>(instances[118]);
    check<int>(instances[119]);
    check<int>(instances[120]);
    check<int>(instances[121]);
    check<int>(instances[122]);
    check<int>(instances[123]);
    check<int>(instances[124]);
    check<int>(instances[125]);
    check<int>(instances[126]);
    check<int>(instances[127]);
    check<int>(instances[128]);
    check<int>(instances[129]);
    check<int>(instances[130]);
    check<int>(instances[131]);
    check<int>(instances[132]);
    check<int>(instances[133]);
    check<int>(instances[134]);
    check<int>(instances[135]);
    check<int>(instances[136]);
    check<int>(instances[137]);
    check<int>(instances[138]);
    check<int>(instances[139]);
    check<int>(instances[140]);
    check<int>(instances[141]);
    check<int>(instances[142]);
    check<int>(instances[143]);
    check<int>(instances[144]);
    check<int>(instances[145]);
    check<int>(instances[146]);
    check<int>(instances[147]);
    check<int>(instances[148]);
    check<int>(instances[149]);
    check<int>(instances[150]);
    check<int>(instances[151]);
    check<int>(instances[152]);
    check<int>(instances[153]);
    check<int>(instances[154]);
    check<int>(instances[155]);
    check<int>(instances[156]);
    check<int>(instances[157]);
    check<int>(instances[158]);
    check<int>(instances[159]);
    check<int>(instances[160]);
    check<int>(instances[161]);
    check<int>(instances[162]);
    check<int>(instances[163]);
    check<int>(instances[164]);
    check<int>(instances[165]);
    check<int>(instances[166]);
    check<int>(instances[167]);
    check<int>(instances[168]);
    check<int>(instances[169]);
    check<int>(instances[170]);
    check<int>(instances[171]);
    check<int>(instances[172]);
    check<int>(instances[173]);
    check<int>(instances[174]);
    check<int>(instances[175]);
    check<int>(instances[176]);
    check<int>(instances[177]);
    check<int>(instances[178]);
    check<int>(instances[179]);
    check<int>(instances[180]);
    check<int>(instances[181]);
    check<int>(instances[182]);
    check<int>(instances[183]);
    check<int>(instances[184]);
    check<int>(instances[185]);
    check<int>(instances[186]);
    check<int>(instances[187]);
    check<int>(instances[188]);
    check<int>(instances[189]);
    check<int>(instances[190]);
    check<int>(instances[191]);
    check<int>(instances[192]);
    check<int>(instances[193]);
    check<int>(instances[194]);
    check<int>(instances[195]);
    check<int>(instances[196]);
    check<int>(instances[197]);
    check<int>(instances[198]);
    check<int>(instances[199]);
    check<int>(instances[200]);
    check<int>(instances[201]);
    check<int>(instances[202]);
    check<int>(instances[203]);
    check<int>(instances[204]);
    check<int>(instances[205]);
    check<int>(instances[206]);
    check<int>(instances[207]);
    check<int>(instances[208]);
    check<int>(instances[209]);
    check<int>(instances[210]);
    check<int>(instances[211]);
    check<int>(instances[212]);
    check<int>(instances[213]);
    check<int>(instances[214]);
    check<int>(instances[215]);
    check<int>(instances[216]);
    check<int>(instances[217]);
    check<int>(instances[218]);
    check<int>(instances[219]);
    check<int>(instances[220]);
    check<int>(instances[221]);
    check<int>(instances[222]);
    check<int>(instances[223]);
    check<int>(instances[224]);
    check<int>(instances[225]);
    check<int>(instances[226]);
    check<int>(instances[227]);
    check<int>(instances[228]);
    check<int>(instances[229]);
    check<int>(instances[230]);
    check<int>(instances[231]);
    check<int>(instances[232]);
    check<int>(instances[233]);
    check<int>(instances[234]);
    check<int>(instances[235]);
    check<int>(instances[236]);
    check<int>(instances[237]);
    check<int>(instances[238]);
    check<int>(instances[239]);
    check<int>(instances[240]);
    check<int>(instances[241]);
    check<int>(instances[242]);
    check<int>(instances[243]);
    check<int>(instances[244]);
    check<int>(instances[245]);
    check<int>(instances[246]);
    check<int>(instances[247]);
    check<int>(instances[248]);
    check<int>(instances[249]);
  }
}

class STC500 extends STCBenchmarkBase {
  const STC500() : super('SubtypeTestCache.STC500', 500);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
    check<int>(instances[75]);
    check<int>(instances[76]);
    check<int>(instances[77]);
    check<int>(instances[78]);
    check<int>(instances[79]);
    check<int>(instances[80]);
    check<int>(instances[81]);
    check<int>(instances[82]);
    check<int>(instances[83]);
    check<int>(instances[84]);
    check<int>(instances[85]);
    check<int>(instances[86]);
    check<int>(instances[87]);
    check<int>(instances[88]);
    check<int>(instances[89]);
    check<int>(instances[90]);
    check<int>(instances[91]);
    check<int>(instances[92]);
    check<int>(instances[93]);
    check<int>(instances[94]);
    check<int>(instances[95]);
    check<int>(instances[96]);
    check<int>(instances[97]);
    check<int>(instances[98]);
    check<int>(instances[99]);
    check<int>(instances[100]);
    check<int>(instances[101]);
    check<int>(instances[102]);
    check<int>(instances[103]);
    check<int>(instances[104]);
    check<int>(instances[105]);
    check<int>(instances[106]);
    check<int>(instances[107]);
    check<int>(instances[108]);
    check<int>(instances[109]);
    check<int>(instances[110]);
    check<int>(instances[111]);
    check<int>(instances[112]);
    check<int>(instances[113]);
    check<int>(instances[114]);
    check<int>(instances[115]);
    check<int>(instances[116]);
    check<int>(instances[117]);
    check<int>(instances[118]);
    check<int>(instances[119]);
    check<int>(instances[120]);
    check<int>(instances[121]);
    check<int>(instances[122]);
    check<int>(instances[123]);
    check<int>(instances[124]);
    check<int>(instances[125]);
    check<int>(instances[126]);
    check<int>(instances[127]);
    check<int>(instances[128]);
    check<int>(instances[129]);
    check<int>(instances[130]);
    check<int>(instances[131]);
    check<int>(instances[132]);
    check<int>(instances[133]);
    check<int>(instances[134]);
    check<int>(instances[135]);
    check<int>(instances[136]);
    check<int>(instances[137]);
    check<int>(instances[138]);
    check<int>(instances[139]);
    check<int>(instances[140]);
    check<int>(instances[141]);
    check<int>(instances[142]);
    check<int>(instances[143]);
    check<int>(instances[144]);
    check<int>(instances[145]);
    check<int>(instances[146]);
    check<int>(instances[147]);
    check<int>(instances[148]);
    check<int>(instances[149]);
    check<int>(instances[150]);
    check<int>(instances[151]);
    check<int>(instances[152]);
    check<int>(instances[153]);
    check<int>(instances[154]);
    check<int>(instances[155]);
    check<int>(instances[156]);
    check<int>(instances[157]);
    check<int>(instances[158]);
    check<int>(instances[159]);
    check<int>(instances[160]);
    check<int>(instances[161]);
    check<int>(instances[162]);
    check<int>(instances[163]);
    check<int>(instances[164]);
    check<int>(instances[165]);
    check<int>(instances[166]);
    check<int>(instances[167]);
    check<int>(instances[168]);
    check<int>(instances[169]);
    check<int>(instances[170]);
    check<int>(instances[171]);
    check<int>(instances[172]);
    check<int>(instances[173]);
    check<int>(instances[174]);
    check<int>(instances[175]);
    check<int>(instances[176]);
    check<int>(instances[177]);
    check<int>(instances[178]);
    check<int>(instances[179]);
    check<int>(instances[180]);
    check<int>(instances[181]);
    check<int>(instances[182]);
    check<int>(instances[183]);
    check<int>(instances[184]);
    check<int>(instances[185]);
    check<int>(instances[186]);
    check<int>(instances[187]);
    check<int>(instances[188]);
    check<int>(instances[189]);
    check<int>(instances[190]);
    check<int>(instances[191]);
    check<int>(instances[192]);
    check<int>(instances[193]);
    check<int>(instances[194]);
    check<int>(instances[195]);
    check<int>(instances[196]);
    check<int>(instances[197]);
    check<int>(instances[198]);
    check<int>(instances[199]);
    check<int>(instances[200]);
    check<int>(instances[201]);
    check<int>(instances[202]);
    check<int>(instances[203]);
    check<int>(instances[204]);
    check<int>(instances[205]);
    check<int>(instances[206]);
    check<int>(instances[207]);
    check<int>(instances[208]);
    check<int>(instances[209]);
    check<int>(instances[210]);
    check<int>(instances[211]);
    check<int>(instances[212]);
    check<int>(instances[213]);
    check<int>(instances[214]);
    check<int>(instances[215]);
    check<int>(instances[216]);
    check<int>(instances[217]);
    check<int>(instances[218]);
    check<int>(instances[219]);
    check<int>(instances[220]);
    check<int>(instances[221]);
    check<int>(instances[222]);
    check<int>(instances[223]);
    check<int>(instances[224]);
    check<int>(instances[225]);
    check<int>(instances[226]);
    check<int>(instances[227]);
    check<int>(instances[228]);
    check<int>(instances[229]);
    check<int>(instances[230]);
    check<int>(instances[231]);
    check<int>(instances[232]);
    check<int>(instances[233]);
    check<int>(instances[234]);
    check<int>(instances[235]);
    check<int>(instances[236]);
    check<int>(instances[237]);
    check<int>(instances[238]);
    check<int>(instances[239]);
    check<int>(instances[240]);
    check<int>(instances[241]);
    check<int>(instances[242]);
    check<int>(instances[243]);
    check<int>(instances[244]);
    check<int>(instances[245]);
    check<int>(instances[246]);
    check<int>(instances[247]);
    check<int>(instances[248]);
    check<int>(instances[249]);
    check<int>(instances[250]);
    check<int>(instances[251]);
    check<int>(instances[252]);
    check<int>(instances[253]);
    check<int>(instances[254]);
    check<int>(instances[255]);
    check<int>(instances[256]);
    check<int>(instances[257]);
    check<int>(instances[258]);
    check<int>(instances[259]);
    check<int>(instances[260]);
    check<int>(instances[261]);
    check<int>(instances[262]);
    check<int>(instances[263]);
    check<int>(instances[264]);
    check<int>(instances[265]);
    check<int>(instances[266]);
    check<int>(instances[267]);
    check<int>(instances[268]);
    check<int>(instances[269]);
    check<int>(instances[270]);
    check<int>(instances[271]);
    check<int>(instances[272]);
    check<int>(instances[273]);
    check<int>(instances[274]);
    check<int>(instances[275]);
    check<int>(instances[276]);
    check<int>(instances[277]);
    check<int>(instances[278]);
    check<int>(instances[279]);
    check<int>(instances[280]);
    check<int>(instances[281]);
    check<int>(instances[282]);
    check<int>(instances[283]);
    check<int>(instances[284]);
    check<int>(instances[285]);
    check<int>(instances[286]);
    check<int>(instances[287]);
    check<int>(instances[288]);
    check<int>(instances[289]);
    check<int>(instances[290]);
    check<int>(instances[291]);
    check<int>(instances[292]);
    check<int>(instances[293]);
    check<int>(instances[294]);
    check<int>(instances[295]);
    check<int>(instances[296]);
    check<int>(instances[297]);
    check<int>(instances[298]);
    check<int>(instances[299]);
    check<int>(instances[300]);
    check<int>(instances[301]);
    check<int>(instances[302]);
    check<int>(instances[303]);
    check<int>(instances[304]);
    check<int>(instances[305]);
    check<int>(instances[306]);
    check<int>(instances[307]);
    check<int>(instances[308]);
    check<int>(instances[309]);
    check<int>(instances[310]);
    check<int>(instances[311]);
    check<int>(instances[312]);
    check<int>(instances[313]);
    check<int>(instances[314]);
    check<int>(instances[315]);
    check<int>(instances[316]);
    check<int>(instances[317]);
    check<int>(instances[318]);
    check<int>(instances[319]);
    check<int>(instances[320]);
    check<int>(instances[321]);
    check<int>(instances[322]);
    check<int>(instances[323]);
    check<int>(instances[324]);
    check<int>(instances[325]);
    check<int>(instances[326]);
    check<int>(instances[327]);
    check<int>(instances[328]);
    check<int>(instances[329]);
    check<int>(instances[330]);
    check<int>(instances[331]);
    check<int>(instances[332]);
    check<int>(instances[333]);
    check<int>(instances[334]);
    check<int>(instances[335]);
    check<int>(instances[336]);
    check<int>(instances[337]);
    check<int>(instances[338]);
    check<int>(instances[339]);
    check<int>(instances[340]);
    check<int>(instances[341]);
    check<int>(instances[342]);
    check<int>(instances[343]);
    check<int>(instances[344]);
    check<int>(instances[345]);
    check<int>(instances[346]);
    check<int>(instances[347]);
    check<int>(instances[348]);
    check<int>(instances[349]);
    check<int>(instances[350]);
    check<int>(instances[351]);
    check<int>(instances[352]);
    check<int>(instances[353]);
    check<int>(instances[354]);
    check<int>(instances[355]);
    check<int>(instances[356]);
    check<int>(instances[357]);
    check<int>(instances[358]);
    check<int>(instances[359]);
    check<int>(instances[360]);
    check<int>(instances[361]);
    check<int>(instances[362]);
    check<int>(instances[363]);
    check<int>(instances[364]);
    check<int>(instances[365]);
    check<int>(instances[366]);
    check<int>(instances[367]);
    check<int>(instances[368]);
    check<int>(instances[369]);
    check<int>(instances[370]);
    check<int>(instances[371]);
    check<int>(instances[372]);
    check<int>(instances[373]);
    check<int>(instances[374]);
    check<int>(instances[375]);
    check<int>(instances[376]);
    check<int>(instances[377]);
    check<int>(instances[378]);
    check<int>(instances[379]);
    check<int>(instances[380]);
    check<int>(instances[381]);
    check<int>(instances[382]);
    check<int>(instances[383]);
    check<int>(instances[384]);
    check<int>(instances[385]);
    check<int>(instances[386]);
    check<int>(instances[387]);
    check<int>(instances[388]);
    check<int>(instances[389]);
    check<int>(instances[390]);
    check<int>(instances[391]);
    check<int>(instances[392]);
    check<int>(instances[393]);
    check<int>(instances[394]);
    check<int>(instances[395]);
    check<int>(instances[396]);
    check<int>(instances[397]);
    check<int>(instances[398]);
    check<int>(instances[399]);
    check<int>(instances[400]);
    check<int>(instances[401]);
    check<int>(instances[402]);
    check<int>(instances[403]);
    check<int>(instances[404]);
    check<int>(instances[405]);
    check<int>(instances[406]);
    check<int>(instances[407]);
    check<int>(instances[408]);
    check<int>(instances[409]);
    check<int>(instances[410]);
    check<int>(instances[411]);
    check<int>(instances[412]);
    check<int>(instances[413]);
    check<int>(instances[414]);
    check<int>(instances[415]);
    check<int>(instances[416]);
    check<int>(instances[417]);
    check<int>(instances[418]);
    check<int>(instances[419]);
    check<int>(instances[420]);
    check<int>(instances[421]);
    check<int>(instances[422]);
    check<int>(instances[423]);
    check<int>(instances[424]);
    check<int>(instances[425]);
    check<int>(instances[426]);
    check<int>(instances[427]);
    check<int>(instances[428]);
    check<int>(instances[429]);
    check<int>(instances[430]);
    check<int>(instances[431]);
    check<int>(instances[432]);
    check<int>(instances[433]);
    check<int>(instances[434]);
    check<int>(instances[435]);
    check<int>(instances[436]);
    check<int>(instances[437]);
    check<int>(instances[438]);
    check<int>(instances[439]);
    check<int>(instances[440]);
    check<int>(instances[441]);
    check<int>(instances[442]);
    check<int>(instances[443]);
    check<int>(instances[444]);
    check<int>(instances[445]);
    check<int>(instances[446]);
    check<int>(instances[447]);
    check<int>(instances[448]);
    check<int>(instances[449]);
    check<int>(instances[450]);
    check<int>(instances[451]);
    check<int>(instances[452]);
    check<int>(instances[453]);
    check<int>(instances[454]);
    check<int>(instances[455]);
    check<int>(instances[456]);
    check<int>(instances[457]);
    check<int>(instances[458]);
    check<int>(instances[459]);
    check<int>(instances[460]);
    check<int>(instances[461]);
    check<int>(instances[462]);
    check<int>(instances[463]);
    check<int>(instances[464]);
    check<int>(instances[465]);
    check<int>(instances[466]);
    check<int>(instances[467]);
    check<int>(instances[468]);
    check<int>(instances[469]);
    check<int>(instances[470]);
    check<int>(instances[471]);
    check<int>(instances[472]);
    check<int>(instances[473]);
    check<int>(instances[474]);
    check<int>(instances[475]);
    check<int>(instances[476]);
    check<int>(instances[477]);
    check<int>(instances[478]);
    check<int>(instances[479]);
    check<int>(instances[480]);
    check<int>(instances[481]);
    check<int>(instances[482]);
    check<int>(instances[483]);
    check<int>(instances[484]);
    check<int>(instances[485]);
    check<int>(instances[486]);
    check<int>(instances[487]);
    check<int>(instances[488]);
    check<int>(instances[489]);
    check<int>(instances[490]);
    check<int>(instances[491]);
    check<int>(instances[492]);
    check<int>(instances[493]);
    check<int>(instances[494]);
    check<int>(instances[495]);
    check<int>(instances[496]);
    check<int>(instances[497]);
    check<int>(instances[498]);
    check<int>(instances[499]);
  }
}

class STC750 extends STCBenchmarkBase {
  const STC750() : super('SubtypeTestCache.STC750', 750);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
    check<int>(instances[75]);
    check<int>(instances[76]);
    check<int>(instances[77]);
    check<int>(instances[78]);
    check<int>(instances[79]);
    check<int>(instances[80]);
    check<int>(instances[81]);
    check<int>(instances[82]);
    check<int>(instances[83]);
    check<int>(instances[84]);
    check<int>(instances[85]);
    check<int>(instances[86]);
    check<int>(instances[87]);
    check<int>(instances[88]);
    check<int>(instances[89]);
    check<int>(instances[90]);
    check<int>(instances[91]);
    check<int>(instances[92]);
    check<int>(instances[93]);
    check<int>(instances[94]);
    check<int>(instances[95]);
    check<int>(instances[96]);
    check<int>(instances[97]);
    check<int>(instances[98]);
    check<int>(instances[99]);
    check<int>(instances[100]);
    check<int>(instances[101]);
    check<int>(instances[102]);
    check<int>(instances[103]);
    check<int>(instances[104]);
    check<int>(instances[105]);
    check<int>(instances[106]);
    check<int>(instances[107]);
    check<int>(instances[108]);
    check<int>(instances[109]);
    check<int>(instances[110]);
    check<int>(instances[111]);
    check<int>(instances[112]);
    check<int>(instances[113]);
    check<int>(instances[114]);
    check<int>(instances[115]);
    check<int>(instances[116]);
    check<int>(instances[117]);
    check<int>(instances[118]);
    check<int>(instances[119]);
    check<int>(instances[120]);
    check<int>(instances[121]);
    check<int>(instances[122]);
    check<int>(instances[123]);
    check<int>(instances[124]);
    check<int>(instances[125]);
    check<int>(instances[126]);
    check<int>(instances[127]);
    check<int>(instances[128]);
    check<int>(instances[129]);
    check<int>(instances[130]);
    check<int>(instances[131]);
    check<int>(instances[132]);
    check<int>(instances[133]);
    check<int>(instances[134]);
    check<int>(instances[135]);
    check<int>(instances[136]);
    check<int>(instances[137]);
    check<int>(instances[138]);
    check<int>(instances[139]);
    check<int>(instances[140]);
    check<int>(instances[141]);
    check<int>(instances[142]);
    check<int>(instances[143]);
    check<int>(instances[144]);
    check<int>(instances[145]);
    check<int>(instances[146]);
    check<int>(instances[147]);
    check<int>(instances[148]);
    check<int>(instances[149]);
    check<int>(instances[150]);
    check<int>(instances[151]);
    check<int>(instances[152]);
    check<int>(instances[153]);
    check<int>(instances[154]);
    check<int>(instances[155]);
    check<int>(instances[156]);
    check<int>(instances[157]);
    check<int>(instances[158]);
    check<int>(instances[159]);
    check<int>(instances[160]);
    check<int>(instances[161]);
    check<int>(instances[162]);
    check<int>(instances[163]);
    check<int>(instances[164]);
    check<int>(instances[165]);
    check<int>(instances[166]);
    check<int>(instances[167]);
    check<int>(instances[168]);
    check<int>(instances[169]);
    check<int>(instances[170]);
    check<int>(instances[171]);
    check<int>(instances[172]);
    check<int>(instances[173]);
    check<int>(instances[174]);
    check<int>(instances[175]);
    check<int>(instances[176]);
    check<int>(instances[177]);
    check<int>(instances[178]);
    check<int>(instances[179]);
    check<int>(instances[180]);
    check<int>(instances[181]);
    check<int>(instances[182]);
    check<int>(instances[183]);
    check<int>(instances[184]);
    check<int>(instances[185]);
    check<int>(instances[186]);
    check<int>(instances[187]);
    check<int>(instances[188]);
    check<int>(instances[189]);
    check<int>(instances[190]);
    check<int>(instances[191]);
    check<int>(instances[192]);
    check<int>(instances[193]);
    check<int>(instances[194]);
    check<int>(instances[195]);
    check<int>(instances[196]);
    check<int>(instances[197]);
    check<int>(instances[198]);
    check<int>(instances[199]);
    check<int>(instances[200]);
    check<int>(instances[201]);
    check<int>(instances[202]);
    check<int>(instances[203]);
    check<int>(instances[204]);
    check<int>(instances[205]);
    check<int>(instances[206]);
    check<int>(instances[207]);
    check<int>(instances[208]);
    check<int>(instances[209]);
    check<int>(instances[210]);
    check<int>(instances[211]);
    check<int>(instances[212]);
    check<int>(instances[213]);
    check<int>(instances[214]);
    check<int>(instances[215]);
    check<int>(instances[216]);
    check<int>(instances[217]);
    check<int>(instances[218]);
    check<int>(instances[219]);
    check<int>(instances[220]);
    check<int>(instances[221]);
    check<int>(instances[222]);
    check<int>(instances[223]);
    check<int>(instances[224]);
    check<int>(instances[225]);
    check<int>(instances[226]);
    check<int>(instances[227]);
    check<int>(instances[228]);
    check<int>(instances[229]);
    check<int>(instances[230]);
    check<int>(instances[231]);
    check<int>(instances[232]);
    check<int>(instances[233]);
    check<int>(instances[234]);
    check<int>(instances[235]);
    check<int>(instances[236]);
    check<int>(instances[237]);
    check<int>(instances[238]);
    check<int>(instances[239]);
    check<int>(instances[240]);
    check<int>(instances[241]);
    check<int>(instances[242]);
    check<int>(instances[243]);
    check<int>(instances[244]);
    check<int>(instances[245]);
    check<int>(instances[246]);
    check<int>(instances[247]);
    check<int>(instances[248]);
    check<int>(instances[249]);
    check<int>(instances[250]);
    check<int>(instances[251]);
    check<int>(instances[252]);
    check<int>(instances[253]);
    check<int>(instances[254]);
    check<int>(instances[255]);
    check<int>(instances[256]);
    check<int>(instances[257]);
    check<int>(instances[258]);
    check<int>(instances[259]);
    check<int>(instances[260]);
    check<int>(instances[261]);
    check<int>(instances[262]);
    check<int>(instances[263]);
    check<int>(instances[264]);
    check<int>(instances[265]);
    check<int>(instances[266]);
    check<int>(instances[267]);
    check<int>(instances[268]);
    check<int>(instances[269]);
    check<int>(instances[270]);
    check<int>(instances[271]);
    check<int>(instances[272]);
    check<int>(instances[273]);
    check<int>(instances[274]);
    check<int>(instances[275]);
    check<int>(instances[276]);
    check<int>(instances[277]);
    check<int>(instances[278]);
    check<int>(instances[279]);
    check<int>(instances[280]);
    check<int>(instances[281]);
    check<int>(instances[282]);
    check<int>(instances[283]);
    check<int>(instances[284]);
    check<int>(instances[285]);
    check<int>(instances[286]);
    check<int>(instances[287]);
    check<int>(instances[288]);
    check<int>(instances[289]);
    check<int>(instances[290]);
    check<int>(instances[291]);
    check<int>(instances[292]);
    check<int>(instances[293]);
    check<int>(instances[294]);
    check<int>(instances[295]);
    check<int>(instances[296]);
    check<int>(instances[297]);
    check<int>(instances[298]);
    check<int>(instances[299]);
    check<int>(instances[300]);
    check<int>(instances[301]);
    check<int>(instances[302]);
    check<int>(instances[303]);
    check<int>(instances[304]);
    check<int>(instances[305]);
    check<int>(instances[306]);
    check<int>(instances[307]);
    check<int>(instances[308]);
    check<int>(instances[309]);
    check<int>(instances[310]);
    check<int>(instances[311]);
    check<int>(instances[312]);
    check<int>(instances[313]);
    check<int>(instances[314]);
    check<int>(instances[315]);
    check<int>(instances[316]);
    check<int>(instances[317]);
    check<int>(instances[318]);
    check<int>(instances[319]);
    check<int>(instances[320]);
    check<int>(instances[321]);
    check<int>(instances[322]);
    check<int>(instances[323]);
    check<int>(instances[324]);
    check<int>(instances[325]);
    check<int>(instances[326]);
    check<int>(instances[327]);
    check<int>(instances[328]);
    check<int>(instances[329]);
    check<int>(instances[330]);
    check<int>(instances[331]);
    check<int>(instances[332]);
    check<int>(instances[333]);
    check<int>(instances[334]);
    check<int>(instances[335]);
    check<int>(instances[336]);
    check<int>(instances[337]);
    check<int>(instances[338]);
    check<int>(instances[339]);
    check<int>(instances[340]);
    check<int>(instances[341]);
    check<int>(instances[342]);
    check<int>(instances[343]);
    check<int>(instances[344]);
    check<int>(instances[345]);
    check<int>(instances[346]);
    check<int>(instances[347]);
    check<int>(instances[348]);
    check<int>(instances[349]);
    check<int>(instances[350]);
    check<int>(instances[351]);
    check<int>(instances[352]);
    check<int>(instances[353]);
    check<int>(instances[354]);
    check<int>(instances[355]);
    check<int>(instances[356]);
    check<int>(instances[357]);
    check<int>(instances[358]);
    check<int>(instances[359]);
    check<int>(instances[360]);
    check<int>(instances[361]);
    check<int>(instances[362]);
    check<int>(instances[363]);
    check<int>(instances[364]);
    check<int>(instances[365]);
    check<int>(instances[366]);
    check<int>(instances[367]);
    check<int>(instances[368]);
    check<int>(instances[369]);
    check<int>(instances[370]);
    check<int>(instances[371]);
    check<int>(instances[372]);
    check<int>(instances[373]);
    check<int>(instances[374]);
    check<int>(instances[375]);
    check<int>(instances[376]);
    check<int>(instances[377]);
    check<int>(instances[378]);
    check<int>(instances[379]);
    check<int>(instances[380]);
    check<int>(instances[381]);
    check<int>(instances[382]);
    check<int>(instances[383]);
    check<int>(instances[384]);
    check<int>(instances[385]);
    check<int>(instances[386]);
    check<int>(instances[387]);
    check<int>(instances[388]);
    check<int>(instances[389]);
    check<int>(instances[390]);
    check<int>(instances[391]);
    check<int>(instances[392]);
    check<int>(instances[393]);
    check<int>(instances[394]);
    check<int>(instances[395]);
    check<int>(instances[396]);
    check<int>(instances[397]);
    check<int>(instances[398]);
    check<int>(instances[399]);
    check<int>(instances[400]);
    check<int>(instances[401]);
    check<int>(instances[402]);
    check<int>(instances[403]);
    check<int>(instances[404]);
    check<int>(instances[405]);
    check<int>(instances[406]);
    check<int>(instances[407]);
    check<int>(instances[408]);
    check<int>(instances[409]);
    check<int>(instances[410]);
    check<int>(instances[411]);
    check<int>(instances[412]);
    check<int>(instances[413]);
    check<int>(instances[414]);
    check<int>(instances[415]);
    check<int>(instances[416]);
    check<int>(instances[417]);
    check<int>(instances[418]);
    check<int>(instances[419]);
    check<int>(instances[420]);
    check<int>(instances[421]);
    check<int>(instances[422]);
    check<int>(instances[423]);
    check<int>(instances[424]);
    check<int>(instances[425]);
    check<int>(instances[426]);
    check<int>(instances[427]);
    check<int>(instances[428]);
    check<int>(instances[429]);
    check<int>(instances[430]);
    check<int>(instances[431]);
    check<int>(instances[432]);
    check<int>(instances[433]);
    check<int>(instances[434]);
    check<int>(instances[435]);
    check<int>(instances[436]);
    check<int>(instances[437]);
    check<int>(instances[438]);
    check<int>(instances[439]);
    check<int>(instances[440]);
    check<int>(instances[441]);
    check<int>(instances[442]);
    check<int>(instances[443]);
    check<int>(instances[444]);
    check<int>(instances[445]);
    check<int>(instances[446]);
    check<int>(instances[447]);
    check<int>(instances[448]);
    check<int>(instances[449]);
    check<int>(instances[450]);
    check<int>(instances[451]);
    check<int>(instances[452]);
    check<int>(instances[453]);
    check<int>(instances[454]);
    check<int>(instances[455]);
    check<int>(instances[456]);
    check<int>(instances[457]);
    check<int>(instances[458]);
    check<int>(instances[459]);
    check<int>(instances[460]);
    check<int>(instances[461]);
    check<int>(instances[462]);
    check<int>(instances[463]);
    check<int>(instances[464]);
    check<int>(instances[465]);
    check<int>(instances[466]);
    check<int>(instances[467]);
    check<int>(instances[468]);
    check<int>(instances[469]);
    check<int>(instances[470]);
    check<int>(instances[471]);
    check<int>(instances[472]);
    check<int>(instances[473]);
    check<int>(instances[474]);
    check<int>(instances[475]);
    check<int>(instances[476]);
    check<int>(instances[477]);
    check<int>(instances[478]);
    check<int>(instances[479]);
    check<int>(instances[480]);
    check<int>(instances[481]);
    check<int>(instances[482]);
    check<int>(instances[483]);
    check<int>(instances[484]);
    check<int>(instances[485]);
    check<int>(instances[486]);
    check<int>(instances[487]);
    check<int>(instances[488]);
    check<int>(instances[489]);
    check<int>(instances[490]);
    check<int>(instances[491]);
    check<int>(instances[492]);
    check<int>(instances[493]);
    check<int>(instances[494]);
    check<int>(instances[495]);
    check<int>(instances[496]);
    check<int>(instances[497]);
    check<int>(instances[498]);
    check<int>(instances[499]);
    check<int>(instances[500]);
    check<int>(instances[501]);
    check<int>(instances[502]);
    check<int>(instances[503]);
    check<int>(instances[504]);
    check<int>(instances[505]);
    check<int>(instances[506]);
    check<int>(instances[507]);
    check<int>(instances[508]);
    check<int>(instances[509]);
    check<int>(instances[510]);
    check<int>(instances[511]);
    check<int>(instances[512]);
    check<int>(instances[513]);
    check<int>(instances[514]);
    check<int>(instances[515]);
    check<int>(instances[516]);
    check<int>(instances[517]);
    check<int>(instances[518]);
    check<int>(instances[519]);
    check<int>(instances[520]);
    check<int>(instances[521]);
    check<int>(instances[522]);
    check<int>(instances[523]);
    check<int>(instances[524]);
    check<int>(instances[525]);
    check<int>(instances[526]);
    check<int>(instances[527]);
    check<int>(instances[528]);
    check<int>(instances[529]);
    check<int>(instances[530]);
    check<int>(instances[531]);
    check<int>(instances[532]);
    check<int>(instances[533]);
    check<int>(instances[534]);
    check<int>(instances[535]);
    check<int>(instances[536]);
    check<int>(instances[537]);
    check<int>(instances[538]);
    check<int>(instances[539]);
    check<int>(instances[540]);
    check<int>(instances[541]);
    check<int>(instances[542]);
    check<int>(instances[543]);
    check<int>(instances[544]);
    check<int>(instances[545]);
    check<int>(instances[546]);
    check<int>(instances[547]);
    check<int>(instances[548]);
    check<int>(instances[549]);
    check<int>(instances[550]);
    check<int>(instances[551]);
    check<int>(instances[552]);
    check<int>(instances[553]);
    check<int>(instances[554]);
    check<int>(instances[555]);
    check<int>(instances[556]);
    check<int>(instances[557]);
    check<int>(instances[558]);
    check<int>(instances[559]);
    check<int>(instances[560]);
    check<int>(instances[561]);
    check<int>(instances[562]);
    check<int>(instances[563]);
    check<int>(instances[564]);
    check<int>(instances[565]);
    check<int>(instances[566]);
    check<int>(instances[567]);
    check<int>(instances[568]);
    check<int>(instances[569]);
    check<int>(instances[570]);
    check<int>(instances[571]);
    check<int>(instances[572]);
    check<int>(instances[573]);
    check<int>(instances[574]);
    check<int>(instances[575]);
    check<int>(instances[576]);
    check<int>(instances[577]);
    check<int>(instances[578]);
    check<int>(instances[579]);
    check<int>(instances[580]);
    check<int>(instances[581]);
    check<int>(instances[582]);
    check<int>(instances[583]);
    check<int>(instances[584]);
    check<int>(instances[585]);
    check<int>(instances[586]);
    check<int>(instances[587]);
    check<int>(instances[588]);
    check<int>(instances[589]);
    check<int>(instances[590]);
    check<int>(instances[591]);
    check<int>(instances[592]);
    check<int>(instances[593]);
    check<int>(instances[594]);
    check<int>(instances[595]);
    check<int>(instances[596]);
    check<int>(instances[597]);
    check<int>(instances[598]);
    check<int>(instances[599]);
    check<int>(instances[600]);
    check<int>(instances[601]);
    check<int>(instances[602]);
    check<int>(instances[603]);
    check<int>(instances[604]);
    check<int>(instances[605]);
    check<int>(instances[606]);
    check<int>(instances[607]);
    check<int>(instances[608]);
    check<int>(instances[609]);
    check<int>(instances[610]);
    check<int>(instances[611]);
    check<int>(instances[612]);
    check<int>(instances[613]);
    check<int>(instances[614]);
    check<int>(instances[615]);
    check<int>(instances[616]);
    check<int>(instances[617]);
    check<int>(instances[618]);
    check<int>(instances[619]);
    check<int>(instances[620]);
    check<int>(instances[621]);
    check<int>(instances[622]);
    check<int>(instances[623]);
    check<int>(instances[624]);
    check<int>(instances[625]);
    check<int>(instances[626]);
    check<int>(instances[627]);
    check<int>(instances[628]);
    check<int>(instances[629]);
    check<int>(instances[630]);
    check<int>(instances[631]);
    check<int>(instances[632]);
    check<int>(instances[633]);
    check<int>(instances[634]);
    check<int>(instances[635]);
    check<int>(instances[636]);
    check<int>(instances[637]);
    check<int>(instances[638]);
    check<int>(instances[639]);
    check<int>(instances[640]);
    check<int>(instances[641]);
    check<int>(instances[642]);
    check<int>(instances[643]);
    check<int>(instances[644]);
    check<int>(instances[645]);
    check<int>(instances[646]);
    check<int>(instances[647]);
    check<int>(instances[648]);
    check<int>(instances[649]);
    check<int>(instances[650]);
    check<int>(instances[651]);
    check<int>(instances[652]);
    check<int>(instances[653]);
    check<int>(instances[654]);
    check<int>(instances[655]);
    check<int>(instances[656]);
    check<int>(instances[657]);
    check<int>(instances[658]);
    check<int>(instances[659]);
    check<int>(instances[660]);
    check<int>(instances[661]);
    check<int>(instances[662]);
    check<int>(instances[663]);
    check<int>(instances[664]);
    check<int>(instances[665]);
    check<int>(instances[666]);
    check<int>(instances[667]);
    check<int>(instances[668]);
    check<int>(instances[669]);
    check<int>(instances[670]);
    check<int>(instances[671]);
    check<int>(instances[672]);
    check<int>(instances[673]);
    check<int>(instances[674]);
    check<int>(instances[675]);
    check<int>(instances[676]);
    check<int>(instances[677]);
    check<int>(instances[678]);
    check<int>(instances[679]);
    check<int>(instances[680]);
    check<int>(instances[681]);
    check<int>(instances[682]);
    check<int>(instances[683]);
    check<int>(instances[684]);
    check<int>(instances[685]);
    check<int>(instances[686]);
    check<int>(instances[687]);
    check<int>(instances[688]);
    check<int>(instances[689]);
    check<int>(instances[690]);
    check<int>(instances[691]);
    check<int>(instances[692]);
    check<int>(instances[693]);
    check<int>(instances[694]);
    check<int>(instances[695]);
    check<int>(instances[696]);
    check<int>(instances[697]);
    check<int>(instances[698]);
    check<int>(instances[699]);
    check<int>(instances[700]);
    check<int>(instances[701]);
    check<int>(instances[702]);
    check<int>(instances[703]);
    check<int>(instances[704]);
    check<int>(instances[705]);
    check<int>(instances[706]);
    check<int>(instances[707]);
    check<int>(instances[708]);
    check<int>(instances[709]);
    check<int>(instances[710]);
    check<int>(instances[711]);
    check<int>(instances[712]);
    check<int>(instances[713]);
    check<int>(instances[714]);
    check<int>(instances[715]);
    check<int>(instances[716]);
    check<int>(instances[717]);
    check<int>(instances[718]);
    check<int>(instances[719]);
    check<int>(instances[720]);
    check<int>(instances[721]);
    check<int>(instances[722]);
    check<int>(instances[723]);
    check<int>(instances[724]);
    check<int>(instances[725]);
    check<int>(instances[726]);
    check<int>(instances[727]);
    check<int>(instances[728]);
    check<int>(instances[729]);
    check<int>(instances[730]);
    check<int>(instances[731]);
    check<int>(instances[732]);
    check<int>(instances[733]);
    check<int>(instances[734]);
    check<int>(instances[735]);
    check<int>(instances[736]);
    check<int>(instances[737]);
    check<int>(instances[738]);
    check<int>(instances[739]);
    check<int>(instances[740]);
    check<int>(instances[741]);
    check<int>(instances[742]);
    check<int>(instances[743]);
    check<int>(instances[744]);
    check<int>(instances[745]);
    check<int>(instances[746]);
    check<int>(instances[747]);
    check<int>(instances[748]);
    check<int>(instances[749]);
  }
}

class STC1000 extends STCBenchmarkBase {
  const STC1000() : super('SubtypeTestCache.STC1000', 1000);

  @override
  void run() {
    check<int>(instances[0]);
    check<int>(instances[1]);
    check<int>(instances[2]);
    check<int>(instances[3]);
    check<int>(instances[4]);
    check<int>(instances[5]);
    check<int>(instances[6]);
    check<int>(instances[7]);
    check<int>(instances[8]);
    check<int>(instances[9]);
    check<int>(instances[10]);
    check<int>(instances[11]);
    check<int>(instances[12]);
    check<int>(instances[13]);
    check<int>(instances[14]);
    check<int>(instances[15]);
    check<int>(instances[16]);
    check<int>(instances[17]);
    check<int>(instances[18]);
    check<int>(instances[19]);
    check<int>(instances[20]);
    check<int>(instances[21]);
    check<int>(instances[22]);
    check<int>(instances[23]);
    check<int>(instances[24]);
    check<int>(instances[25]);
    check<int>(instances[26]);
    check<int>(instances[27]);
    check<int>(instances[28]);
    check<int>(instances[29]);
    check<int>(instances[30]);
    check<int>(instances[31]);
    check<int>(instances[32]);
    check<int>(instances[33]);
    check<int>(instances[34]);
    check<int>(instances[35]);
    check<int>(instances[36]);
    check<int>(instances[37]);
    check<int>(instances[38]);
    check<int>(instances[39]);
    check<int>(instances[40]);
    check<int>(instances[41]);
    check<int>(instances[42]);
    check<int>(instances[43]);
    check<int>(instances[44]);
    check<int>(instances[45]);
    check<int>(instances[46]);
    check<int>(instances[47]);
    check<int>(instances[48]);
    check<int>(instances[49]);
    check<int>(instances[50]);
    check<int>(instances[51]);
    check<int>(instances[52]);
    check<int>(instances[53]);
    check<int>(instances[54]);
    check<int>(instances[55]);
    check<int>(instances[56]);
    check<int>(instances[57]);
    check<int>(instances[58]);
    check<int>(instances[59]);
    check<int>(instances[60]);
    check<int>(instances[61]);
    check<int>(instances[62]);
    check<int>(instances[63]);
    check<int>(instances[64]);
    check<int>(instances[65]);
    check<int>(instances[66]);
    check<int>(instances[67]);
    check<int>(instances[68]);
    check<int>(instances[69]);
    check<int>(instances[70]);
    check<int>(instances[71]);
    check<int>(instances[72]);
    check<int>(instances[73]);
    check<int>(instances[74]);
    check<int>(instances[75]);
    check<int>(instances[76]);
    check<int>(instances[77]);
    check<int>(instances[78]);
    check<int>(instances[79]);
    check<int>(instances[80]);
    check<int>(instances[81]);
    check<int>(instances[82]);
    check<int>(instances[83]);
    check<int>(instances[84]);
    check<int>(instances[85]);
    check<int>(instances[86]);
    check<int>(instances[87]);
    check<int>(instances[88]);
    check<int>(instances[89]);
    check<int>(instances[90]);
    check<int>(instances[91]);
    check<int>(instances[92]);
    check<int>(instances[93]);
    check<int>(instances[94]);
    check<int>(instances[95]);
    check<int>(instances[96]);
    check<int>(instances[97]);
    check<int>(instances[98]);
    check<int>(instances[99]);
    check<int>(instances[100]);
    check<int>(instances[101]);
    check<int>(instances[102]);
    check<int>(instances[103]);
    check<int>(instances[104]);
    check<int>(instances[105]);
    check<int>(instances[106]);
    check<int>(instances[107]);
    check<int>(instances[108]);
    check<int>(instances[109]);
    check<int>(instances[110]);
    check<int>(instances[111]);
    check<int>(instances[112]);
    check<int>(instances[113]);
    check<int>(instances[114]);
    check<int>(instances[115]);
    check<int>(instances[116]);
    check<int>(instances[117]);
    check<int>(instances[118]);
    check<int>(instances[119]);
    check<int>(instances[120]);
    check<int>(instances[121]);
    check<int>(instances[122]);
    check<int>(instances[123]);
    check<int>(instances[124]);
    check<int>(instances[125]);
    check<int>(instances[126]);
    check<int>(instances[127]);
    check<int>(instances[128]);
    check<int>(instances[129]);
    check<int>(instances[130]);
    check<int>(instances[131]);
    check<int>(instances[132]);
    check<int>(instances[133]);
    check<int>(instances[134]);
    check<int>(instances[135]);
    check<int>(instances[136]);
    check<int>(instances[137]);
    check<int>(instances[138]);
    check<int>(instances[139]);
    check<int>(instances[140]);
    check<int>(instances[141]);
    check<int>(instances[142]);
    check<int>(instances[143]);
    check<int>(instances[144]);
    check<int>(instances[145]);
    check<int>(instances[146]);
    check<int>(instances[147]);
    check<int>(instances[148]);
    check<int>(instances[149]);
    check<int>(instances[150]);
    check<int>(instances[151]);
    check<int>(instances[152]);
    check<int>(instances[153]);
    check<int>(instances[154]);
    check<int>(instances[155]);
    check<int>(instances[156]);
    check<int>(instances[157]);
    check<int>(instances[158]);
    check<int>(instances[159]);
    check<int>(instances[160]);
    check<int>(instances[161]);
    check<int>(instances[162]);
    check<int>(instances[163]);
    check<int>(instances[164]);
    check<int>(instances[165]);
    check<int>(instances[166]);
    check<int>(instances[167]);
    check<int>(instances[168]);
    check<int>(instances[169]);
    check<int>(instances[170]);
    check<int>(instances[171]);
    check<int>(instances[172]);
    check<int>(instances[173]);
    check<int>(instances[174]);
    check<int>(instances[175]);
    check<int>(instances[176]);
    check<int>(instances[177]);
    check<int>(instances[178]);
    check<int>(instances[179]);
    check<int>(instances[180]);
    check<int>(instances[181]);
    check<int>(instances[182]);
    check<int>(instances[183]);
    check<int>(instances[184]);
    check<int>(instances[185]);
    check<int>(instances[186]);
    check<int>(instances[187]);
    check<int>(instances[188]);
    check<int>(instances[189]);
    check<int>(instances[190]);
    check<int>(instances[191]);
    check<int>(instances[192]);
    check<int>(instances[193]);
    check<int>(instances[194]);
    check<int>(instances[195]);
    check<int>(instances[196]);
    check<int>(instances[197]);
    check<int>(instances[198]);
    check<int>(instances[199]);
    check<int>(instances[200]);
    check<int>(instances[201]);
    check<int>(instances[202]);
    check<int>(instances[203]);
    check<int>(instances[204]);
    check<int>(instances[205]);
    check<int>(instances[206]);
    check<int>(instances[207]);
    check<int>(instances[208]);
    check<int>(instances[209]);
    check<int>(instances[210]);
    check<int>(instances[211]);
    check<int>(instances[212]);
    check<int>(instances[213]);
    check<int>(instances[214]);
    check<int>(instances[215]);
    check<int>(instances[216]);
    check<int>(instances[217]);
    check<int>(instances[218]);
    check<int>(instances[219]);
    check<int>(instances[220]);
    check<int>(instances[221]);
    check<int>(instances[222]);
    check<int>(instances[223]);
    check<int>(instances[224]);
    check<int>(instances[225]);
    check<int>(instances[226]);
    check<int>(instances[227]);
    check<int>(instances[228]);
    check<int>(instances[229]);
    check<int>(instances[230]);
    check<int>(instances[231]);
    check<int>(instances[232]);
    check<int>(instances[233]);
    check<int>(instances[234]);
    check<int>(instances[235]);
    check<int>(instances[236]);
    check<int>(instances[237]);
    check<int>(instances[238]);
    check<int>(instances[239]);
    check<int>(instances[240]);
    check<int>(instances[241]);
    check<int>(instances[242]);
    check<int>(instances[243]);
    check<int>(instances[244]);
    check<int>(instances[245]);
    check<int>(instances[246]);
    check<int>(instances[247]);
    check<int>(instances[248]);
    check<int>(instances[249]);
    check<int>(instances[250]);
    check<int>(instances[251]);
    check<int>(instances[252]);
    check<int>(instances[253]);
    check<int>(instances[254]);
    check<int>(instances[255]);
    check<int>(instances[256]);
    check<int>(instances[257]);
    check<int>(instances[258]);
    check<int>(instances[259]);
    check<int>(instances[260]);
    check<int>(instances[261]);
    check<int>(instances[262]);
    check<int>(instances[263]);
    check<int>(instances[264]);
    check<int>(instances[265]);
    check<int>(instances[266]);
    check<int>(instances[267]);
    check<int>(instances[268]);
    check<int>(instances[269]);
    check<int>(instances[270]);
    check<int>(instances[271]);
    check<int>(instances[272]);
    check<int>(instances[273]);
    check<int>(instances[274]);
    check<int>(instances[275]);
    check<int>(instances[276]);
    check<int>(instances[277]);
    check<int>(instances[278]);
    check<int>(instances[279]);
    check<int>(instances[280]);
    check<int>(instances[281]);
    check<int>(instances[282]);
    check<int>(instances[283]);
    check<int>(instances[284]);
    check<int>(instances[285]);
    check<int>(instances[286]);
    check<int>(instances[287]);
    check<int>(instances[288]);
    check<int>(instances[289]);
    check<int>(instances[290]);
    check<int>(instances[291]);
    check<int>(instances[292]);
    check<int>(instances[293]);
    check<int>(instances[294]);
    check<int>(instances[295]);
    check<int>(instances[296]);
    check<int>(instances[297]);
    check<int>(instances[298]);
    check<int>(instances[299]);
    check<int>(instances[300]);
    check<int>(instances[301]);
    check<int>(instances[302]);
    check<int>(instances[303]);
    check<int>(instances[304]);
    check<int>(instances[305]);
    check<int>(instances[306]);
    check<int>(instances[307]);
    check<int>(instances[308]);
    check<int>(instances[309]);
    check<int>(instances[310]);
    check<int>(instances[311]);
    check<int>(instances[312]);
    check<int>(instances[313]);
    check<int>(instances[314]);
    check<int>(instances[315]);
    check<int>(instances[316]);
    check<int>(instances[317]);
    check<int>(instances[318]);
    check<int>(instances[319]);
    check<int>(instances[320]);
    check<int>(instances[321]);
    check<int>(instances[322]);
    check<int>(instances[323]);
    check<int>(instances[324]);
    check<int>(instances[325]);
    check<int>(instances[326]);
    check<int>(instances[327]);
    check<int>(instances[328]);
    check<int>(instances[329]);
    check<int>(instances[330]);
    check<int>(instances[331]);
    check<int>(instances[332]);
    check<int>(instances[333]);
    check<int>(instances[334]);
    check<int>(instances[335]);
    check<int>(instances[336]);
    check<int>(instances[337]);
    check<int>(instances[338]);
    check<int>(instances[339]);
    check<int>(instances[340]);
    check<int>(instances[341]);
    check<int>(instances[342]);
    check<int>(instances[343]);
    check<int>(instances[344]);
    check<int>(instances[345]);
    check<int>(instances[346]);
    check<int>(instances[347]);
    check<int>(instances[348]);
    check<int>(instances[349]);
    check<int>(instances[350]);
    check<int>(instances[351]);
    check<int>(instances[352]);
    check<int>(instances[353]);
    check<int>(instances[354]);
    check<int>(instances[355]);
    check<int>(instances[356]);
    check<int>(instances[357]);
    check<int>(instances[358]);
    check<int>(instances[359]);
    check<int>(instances[360]);
    check<int>(instances[361]);
    check<int>(instances[362]);
    check<int>(instances[363]);
    check<int>(instances[364]);
    check<int>(instances[365]);
    check<int>(instances[366]);
    check<int>(instances[367]);
    check<int>(instances[368]);
    check<int>(instances[369]);
    check<int>(instances[370]);
    check<int>(instances[371]);
    check<int>(instances[372]);
    check<int>(instances[373]);
    check<int>(instances[374]);
    check<int>(instances[375]);
    check<int>(instances[376]);
    check<int>(instances[377]);
    check<int>(instances[378]);
    check<int>(instances[379]);
    check<int>(instances[380]);
    check<int>(instances[381]);
    check<int>(instances[382]);
    check<int>(instances[383]);
    check<int>(instances[384]);
    check<int>(instances[385]);
    check<int>(instances[386]);
    check<int>(instances[387]);
    check<int>(instances[388]);
    check<int>(instances[389]);
    check<int>(instances[390]);
    check<int>(instances[391]);
    check<int>(instances[392]);
    check<int>(instances[393]);
    check<int>(instances[394]);
    check<int>(instances[395]);
    check<int>(instances[396]);
    check<int>(instances[397]);
    check<int>(instances[398]);
    check<int>(instances[399]);
    check<int>(instances[400]);
    check<int>(instances[401]);
    check<int>(instances[402]);
    check<int>(instances[403]);
    check<int>(instances[404]);
    check<int>(instances[405]);
    check<int>(instances[406]);
    check<int>(instances[407]);
    check<int>(instances[408]);
    check<int>(instances[409]);
    check<int>(instances[410]);
    check<int>(instances[411]);
    check<int>(instances[412]);
    check<int>(instances[413]);
    check<int>(instances[414]);
    check<int>(instances[415]);
    check<int>(instances[416]);
    check<int>(instances[417]);
    check<int>(instances[418]);
    check<int>(instances[419]);
    check<int>(instances[420]);
    check<int>(instances[421]);
    check<int>(instances[422]);
    check<int>(instances[423]);
    check<int>(instances[424]);
    check<int>(instances[425]);
    check<int>(instances[426]);
    check<int>(instances[427]);
    check<int>(instances[428]);
    check<int>(instances[429]);
    check<int>(instances[430]);
    check<int>(instances[431]);
    check<int>(instances[432]);
    check<int>(instances[433]);
    check<int>(instances[434]);
    check<int>(instances[435]);
    check<int>(instances[436]);
    check<int>(instances[437]);
    check<int>(instances[438]);
    check<int>(instances[439]);
    check<int>(instances[440]);
    check<int>(instances[441]);
    check<int>(instances[442]);
    check<int>(instances[443]);
    check<int>(instances[444]);
    check<int>(instances[445]);
    check<int>(instances[446]);
    check<int>(instances[447]);
    check<int>(instances[448]);
    check<int>(instances[449]);
    check<int>(instances[450]);
    check<int>(instances[451]);
    check<int>(instances[452]);
    check<int>(instances[453]);
    check<int>(instances[454]);
    check<int>(instances[455]);
    check<int>(instances[456]);
    check<int>(instances[457]);
    check<int>(instances[458]);
    check<int>(instances[459]);
    check<int>(instances[460]);
    check<int>(instances[461]);
    check<int>(instances[462]);
    check<int>(instances[463]);
    check<int>(instances[464]);
    check<int>(instances[465]);
    check<int>(instances[466]);
    check<int>(instances[467]);
    check<int>(instances[468]);
    check<int>(instances[469]);
    check<int>(instances[470]);
    check<int>(instances[471]);
    check<int>(instances[472]);
    check<int>(instances[473]);
    check<int>(instances[474]);
    check<int>(instances[475]);
    check<int>(instances[476]);
    check<int>(instances[477]);
    check<int>(instances[478]);
    check<int>(instances[479]);
    check<int>(instances[480]);
    check<int>(instances[481]);
    check<int>(instances[482]);
    check<int>(instances[483]);
    check<int>(instances[484]);
    check<int>(instances[485]);
    check<int>(instances[486]);
    check<int>(instances[487]);
    check<int>(instances[488]);
    check<int>(instances[489]);
    check<int>(instances[490]);
    check<int>(instances[491]);
    check<int>(instances[492]);
    check<int>(instances[493]);
    check<int>(instances[494]);
    check<int>(instances[495]);
    check<int>(instances[496]);
    check<int>(instances[497]);
    check<int>(instances[498]);
    check<int>(instances[499]);
    check<int>(instances[500]);
    check<int>(instances[501]);
    check<int>(instances[502]);
    check<int>(instances[503]);
    check<int>(instances[504]);
    check<int>(instances[505]);
    check<int>(instances[506]);
    check<int>(instances[507]);
    check<int>(instances[508]);
    check<int>(instances[509]);
    check<int>(instances[510]);
    check<int>(instances[511]);
    check<int>(instances[512]);
    check<int>(instances[513]);
    check<int>(instances[514]);
    check<int>(instances[515]);
    check<int>(instances[516]);
    check<int>(instances[517]);
    check<int>(instances[518]);
    check<int>(instances[519]);
    check<int>(instances[520]);
    check<int>(instances[521]);
    check<int>(instances[522]);
    check<int>(instances[523]);
    check<int>(instances[524]);
    check<int>(instances[525]);
    check<int>(instances[526]);
    check<int>(instances[527]);
    check<int>(instances[528]);
    check<int>(instances[529]);
    check<int>(instances[530]);
    check<int>(instances[531]);
    check<int>(instances[532]);
    check<int>(instances[533]);
    check<int>(instances[534]);
    check<int>(instances[535]);
    check<int>(instances[536]);
    check<int>(instances[537]);
    check<int>(instances[538]);
    check<int>(instances[539]);
    check<int>(instances[540]);
    check<int>(instances[541]);
    check<int>(instances[542]);
    check<int>(instances[543]);
    check<int>(instances[544]);
    check<int>(instances[545]);
    check<int>(instances[546]);
    check<int>(instances[547]);
    check<int>(instances[548]);
    check<int>(instances[549]);
    check<int>(instances[550]);
    check<int>(instances[551]);
    check<int>(instances[552]);
    check<int>(instances[553]);
    check<int>(instances[554]);
    check<int>(instances[555]);
    check<int>(instances[556]);
    check<int>(instances[557]);
    check<int>(instances[558]);
    check<int>(instances[559]);
    check<int>(instances[560]);
    check<int>(instances[561]);
    check<int>(instances[562]);
    check<int>(instances[563]);
    check<int>(instances[564]);
    check<int>(instances[565]);
    check<int>(instances[566]);
    check<int>(instances[567]);
    check<int>(instances[568]);
    check<int>(instances[569]);
    check<int>(instances[570]);
    check<int>(instances[571]);
    check<int>(instances[572]);
    check<int>(instances[573]);
    check<int>(instances[574]);
    check<int>(instances[575]);
    check<int>(instances[576]);
    check<int>(instances[577]);
    check<int>(instances[578]);
    check<int>(instances[579]);
    check<int>(instances[580]);
    check<int>(instances[581]);
    check<int>(instances[582]);
    check<int>(instances[583]);
    check<int>(instances[584]);
    check<int>(instances[585]);
    check<int>(instances[586]);
    check<int>(instances[587]);
    check<int>(instances[588]);
    check<int>(instances[589]);
    check<int>(instances[590]);
    check<int>(instances[591]);
    check<int>(instances[592]);
    check<int>(instances[593]);
    check<int>(instances[594]);
    check<int>(instances[595]);
    check<int>(instances[596]);
    check<int>(instances[597]);
    check<int>(instances[598]);
    check<int>(instances[599]);
    check<int>(instances[600]);
    check<int>(instances[601]);
    check<int>(instances[602]);
    check<int>(instances[603]);
    check<int>(instances[604]);
    check<int>(instances[605]);
    check<int>(instances[606]);
    check<int>(instances[607]);
    check<int>(instances[608]);
    check<int>(instances[609]);
    check<int>(instances[610]);
    check<int>(instances[611]);
    check<int>(instances[612]);
    check<int>(instances[613]);
    check<int>(instances[614]);
    check<int>(instances[615]);
    check<int>(instances[616]);
    check<int>(instances[617]);
    check<int>(instances[618]);
    check<int>(instances[619]);
    check<int>(instances[620]);
    check<int>(instances[621]);
    check<int>(instances[622]);
    check<int>(instances[623]);
    check<int>(instances[624]);
    check<int>(instances[625]);
    check<int>(instances[626]);
    check<int>(instances[627]);
    check<int>(instances[628]);
    check<int>(instances[629]);
    check<int>(instances[630]);
    check<int>(instances[631]);
    check<int>(instances[632]);
    check<int>(instances[633]);
    check<int>(instances[634]);
    check<int>(instances[635]);
    check<int>(instances[636]);
    check<int>(instances[637]);
    check<int>(instances[638]);
    check<int>(instances[639]);
    check<int>(instances[640]);
    check<int>(instances[641]);
    check<int>(instances[642]);
    check<int>(instances[643]);
    check<int>(instances[644]);
    check<int>(instances[645]);
    check<int>(instances[646]);
    check<int>(instances[647]);
    check<int>(instances[648]);
    check<int>(instances[649]);
    check<int>(instances[650]);
    check<int>(instances[651]);
    check<int>(instances[652]);
    check<int>(instances[653]);
    check<int>(instances[654]);
    check<int>(instances[655]);
    check<int>(instances[656]);
    check<int>(instances[657]);
    check<int>(instances[658]);
    check<int>(instances[659]);
    check<int>(instances[660]);
    check<int>(instances[661]);
    check<int>(instances[662]);
    check<int>(instances[663]);
    check<int>(instances[664]);
    check<int>(instances[665]);
    check<int>(instances[666]);
    check<int>(instances[667]);
    check<int>(instances[668]);
    check<int>(instances[669]);
    check<int>(instances[670]);
    check<int>(instances[671]);
    check<int>(instances[672]);
    check<int>(instances[673]);
    check<int>(instances[674]);
    check<int>(instances[675]);
    check<int>(instances[676]);
    check<int>(instances[677]);
    check<int>(instances[678]);
    check<int>(instances[679]);
    check<int>(instances[680]);
    check<int>(instances[681]);
    check<int>(instances[682]);
    check<int>(instances[683]);
    check<int>(instances[684]);
    check<int>(instances[685]);
    check<int>(instances[686]);
    check<int>(instances[687]);
    check<int>(instances[688]);
    check<int>(instances[689]);
    check<int>(instances[690]);
    check<int>(instances[691]);
    check<int>(instances[692]);
    check<int>(instances[693]);
    check<int>(instances[694]);
    check<int>(instances[695]);
    check<int>(instances[696]);
    check<int>(instances[697]);
    check<int>(instances[698]);
    check<int>(instances[699]);
    check<int>(instances[700]);
    check<int>(instances[701]);
    check<int>(instances[702]);
    check<int>(instances[703]);
    check<int>(instances[704]);
    check<int>(instances[705]);
    check<int>(instances[706]);
    check<int>(instances[707]);
    check<int>(instances[708]);
    check<int>(instances[709]);
    check<int>(instances[710]);
    check<int>(instances[711]);
    check<int>(instances[712]);
    check<int>(instances[713]);
    check<int>(instances[714]);
    check<int>(instances[715]);
    check<int>(instances[716]);
    check<int>(instances[717]);
    check<int>(instances[718]);
    check<int>(instances[719]);
    check<int>(instances[720]);
    check<int>(instances[721]);
    check<int>(instances[722]);
    check<int>(instances[723]);
    check<int>(instances[724]);
    check<int>(instances[725]);
    check<int>(instances[726]);
    check<int>(instances[727]);
    check<int>(instances[728]);
    check<int>(instances[729]);
    check<int>(instances[730]);
    check<int>(instances[731]);
    check<int>(instances[732]);
    check<int>(instances[733]);
    check<int>(instances[734]);
    check<int>(instances[735]);
    check<int>(instances[736]);
    check<int>(instances[737]);
    check<int>(instances[738]);
    check<int>(instances[739]);
    check<int>(instances[740]);
    check<int>(instances[741]);
    check<int>(instances[742]);
    check<int>(instances[743]);
    check<int>(instances[744]);
    check<int>(instances[745]);
    check<int>(instances[746]);
    check<int>(instances[747]);
    check<int>(instances[748]);
    check<int>(instances[749]);
    check<int>(instances[750]);
    check<int>(instances[751]);
    check<int>(instances[752]);
    check<int>(instances[753]);
    check<int>(instances[754]);
    check<int>(instances[755]);
    check<int>(instances[756]);
    check<int>(instances[757]);
    check<int>(instances[758]);
    check<int>(instances[759]);
    check<int>(instances[760]);
    check<int>(instances[761]);
    check<int>(instances[762]);
    check<int>(instances[763]);
    check<int>(instances[764]);
    check<int>(instances[765]);
    check<int>(instances[766]);
    check<int>(instances[767]);
    check<int>(instances[768]);
    check<int>(instances[769]);
    check<int>(instances[770]);
    check<int>(instances[771]);
    check<int>(instances[772]);
    check<int>(instances[773]);
    check<int>(instances[774]);
    check<int>(instances[775]);
    check<int>(instances[776]);
    check<int>(instances[777]);
    check<int>(instances[778]);
    check<int>(instances[779]);
    check<int>(instances[780]);
    check<int>(instances[781]);
    check<int>(instances[782]);
    check<int>(instances[783]);
    check<int>(instances[784]);
    check<int>(instances[785]);
    check<int>(instances[786]);
    check<int>(instances[787]);
    check<int>(instances[788]);
    check<int>(instances[789]);
    check<int>(instances[790]);
    check<int>(instances[791]);
    check<int>(instances[792]);
    check<int>(instances[793]);
    check<int>(instances[794]);
    check<int>(instances[795]);
    check<int>(instances[796]);
    check<int>(instances[797]);
    check<int>(instances[798]);
    check<int>(instances[799]);
    check<int>(instances[800]);
    check<int>(instances[801]);
    check<int>(instances[802]);
    check<int>(instances[803]);
    check<int>(instances[804]);
    check<int>(instances[805]);
    check<int>(instances[806]);
    check<int>(instances[807]);
    check<int>(instances[808]);
    check<int>(instances[809]);
    check<int>(instances[810]);
    check<int>(instances[811]);
    check<int>(instances[812]);
    check<int>(instances[813]);
    check<int>(instances[814]);
    check<int>(instances[815]);
    check<int>(instances[816]);
    check<int>(instances[817]);
    check<int>(instances[818]);
    check<int>(instances[819]);
    check<int>(instances[820]);
    check<int>(instances[821]);
    check<int>(instances[822]);
    check<int>(instances[823]);
    check<int>(instances[824]);
    check<int>(instances[825]);
    check<int>(instances[826]);
    check<int>(instances[827]);
    check<int>(instances[828]);
    check<int>(instances[829]);
    check<int>(instances[830]);
    check<int>(instances[831]);
    check<int>(instances[832]);
    check<int>(instances[833]);
    check<int>(instances[834]);
    check<int>(instances[835]);
    check<int>(instances[836]);
    check<int>(instances[837]);
    check<int>(instances[838]);
    check<int>(instances[839]);
    check<int>(instances[840]);
    check<int>(instances[841]);
    check<int>(instances[842]);
    check<int>(instances[843]);
    check<int>(instances[844]);
    check<int>(instances[845]);
    check<int>(instances[846]);
    check<int>(instances[847]);
    check<int>(instances[848]);
    check<int>(instances[849]);
    check<int>(instances[850]);
    check<int>(instances[851]);
    check<int>(instances[852]);
    check<int>(instances[853]);
    check<int>(instances[854]);
    check<int>(instances[855]);
    check<int>(instances[856]);
    check<int>(instances[857]);
    check<int>(instances[858]);
    check<int>(instances[859]);
    check<int>(instances[860]);
    check<int>(instances[861]);
    check<int>(instances[862]);
    check<int>(instances[863]);
    check<int>(instances[864]);
    check<int>(instances[865]);
    check<int>(instances[866]);
    check<int>(instances[867]);
    check<int>(instances[868]);
    check<int>(instances[869]);
    check<int>(instances[870]);
    check<int>(instances[871]);
    check<int>(instances[872]);
    check<int>(instances[873]);
    check<int>(instances[874]);
    check<int>(instances[875]);
    check<int>(instances[876]);
    check<int>(instances[877]);
    check<int>(instances[878]);
    check<int>(instances[879]);
    check<int>(instances[880]);
    check<int>(instances[881]);
    check<int>(instances[882]);
    check<int>(instances[883]);
    check<int>(instances[884]);
    check<int>(instances[885]);
    check<int>(instances[886]);
    check<int>(instances[887]);
    check<int>(instances[888]);
    check<int>(instances[889]);
    check<int>(instances[890]);
    check<int>(instances[891]);
    check<int>(instances[892]);
    check<int>(instances[893]);
    check<int>(instances[894]);
    check<int>(instances[895]);
    check<int>(instances[896]);
    check<int>(instances[897]);
    check<int>(instances[898]);
    check<int>(instances[899]);
    check<int>(instances[900]);
    check<int>(instances[901]);
    check<int>(instances[902]);
    check<int>(instances[903]);
    check<int>(instances[904]);
    check<int>(instances[905]);
    check<int>(instances[906]);
    check<int>(instances[907]);
    check<int>(instances[908]);
    check<int>(instances[909]);
    check<int>(instances[910]);
    check<int>(instances[911]);
    check<int>(instances[912]);
    check<int>(instances[913]);
    check<int>(instances[914]);
    check<int>(instances[915]);
    check<int>(instances[916]);
    check<int>(instances[917]);
    check<int>(instances[918]);
    check<int>(instances[919]);
    check<int>(instances[920]);
    check<int>(instances[921]);
    check<int>(instances[922]);
    check<int>(instances[923]);
    check<int>(instances[924]);
    check<int>(instances[925]);
    check<int>(instances[926]);
    check<int>(instances[927]);
    check<int>(instances[928]);
    check<int>(instances[929]);
    check<int>(instances[930]);
    check<int>(instances[931]);
    check<int>(instances[932]);
    check<int>(instances[933]);
    check<int>(instances[934]);
    check<int>(instances[935]);
    check<int>(instances[936]);
    check<int>(instances[937]);
    check<int>(instances[938]);
    check<int>(instances[939]);
    check<int>(instances[940]);
    check<int>(instances[941]);
    check<int>(instances[942]);
    check<int>(instances[943]);
    check<int>(instances[944]);
    check<int>(instances[945]);
    check<int>(instances[946]);
    check<int>(instances[947]);
    check<int>(instances[948]);
    check<int>(instances[949]);
    check<int>(instances[950]);
    check<int>(instances[951]);
    check<int>(instances[952]);
    check<int>(instances[953]);
    check<int>(instances[954]);
    check<int>(instances[955]);
    check<int>(instances[956]);
    check<int>(instances[957]);
    check<int>(instances[958]);
    check<int>(instances[959]);
    check<int>(instances[960]);
    check<int>(instances[961]);
    check<int>(instances[962]);
    check<int>(instances[963]);
    check<int>(instances[964]);
    check<int>(instances[965]);
    check<int>(instances[966]);
    check<int>(instances[967]);
    check<int>(instances[968]);
    check<int>(instances[969]);
    check<int>(instances[970]);
    check<int>(instances[971]);
    check<int>(instances[972]);
    check<int>(instances[973]);
    check<int>(instances[974]);
    check<int>(instances[975]);
    check<int>(instances[976]);
    check<int>(instances[977]);
    check<int>(instances[978]);
    check<int>(instances[979]);
    check<int>(instances[980]);
    check<int>(instances[981]);
    check<int>(instances[982]);
    check<int>(instances[983]);
    check<int>(instances[984]);
    check<int>(instances[985]);
    check<int>(instances[986]);
    check<int>(instances[987]);
    check<int>(instances[988]);
    check<int>(instances[989]);
    check<int>(instances[990]);
    check<int>(instances[991]);
    check<int>(instances[992]);
    check<int>(instances[993]);
    check<int>(instances[994]);
    check<int>(instances[995]);
    check<int>(instances[996]);
    check<int>(instances[997]);
    check<int>(instances[998]);
    check<int>(instances[999]);
  }
}

class STCSame1000 extends STCBenchmarkBase {
  const STCSame1000() : super('SubtypeTestCache.STCSame1000', 1000);

  @override
  void run() {
    // Do 1000 AssertAssignable checks for the last type checked in the
    // STC1000 benchmark.
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
    check<int>(instances[999]);
  }
}

@pragma('vm:never-inline')
@pragma('wasm:never-inline')
@pragma('dart2js:never-inline')
void check<S>(dynamic s) => s as C<S> Function();

class C<T> {}

class C0<T> extends C<T> {}

C0<S> closure0<S>() => C0<S>();

class C1<T> extends C<T> {}

C1<S> closure1<S>() => C1<S>();

class C2<T> extends C<T> {}

C2<S> closure2<S>() => C2<S>();

class C3<T> extends C<T> {}

C3<S> closure3<S>() => C3<S>();

class C4<T> extends C<T> {}

C4<S> closure4<S>() => C4<S>();

class C5<T> extends C<T> {}

C5<S> closure5<S>() => C5<S>();

class C6<T> extends C<T> {}

C6<S> closure6<S>() => C6<S>();

class C7<T> extends C<T> {}

C7<S> closure7<S>() => C7<S>();

class C8<T> extends C<T> {}

C8<S> closure8<S>() => C8<S>();

class C9<T> extends C<T> {}

C9<S> closure9<S>() => C9<S>();

class C10<T> extends C<T> {}

C10<S> closure10<S>() => C10<S>();

class C11<T> extends C<T> {}

C11<S> closure11<S>() => C11<S>();

class C12<T> extends C<T> {}

C12<S> closure12<S>() => C12<S>();

class C13<T> extends C<T> {}

C13<S> closure13<S>() => C13<S>();

class C14<T> extends C<T> {}

C14<S> closure14<S>() => C14<S>();

class C15<T> extends C<T> {}

C15<S> closure15<S>() => C15<S>();

class C16<T> extends C<T> {}

C16<S> closure16<S>() => C16<S>();

class C17<T> extends C<T> {}

C17<S> closure17<S>() => C17<S>();

class C18<T> extends C<T> {}

C18<S> closure18<S>() => C18<S>();

class C19<T> extends C<T> {}

C19<S> closure19<S>() => C19<S>();

class C20<T> extends C<T> {}

C20<S> closure20<S>() => C20<S>();

class C21<T> extends C<T> {}

C21<S> closure21<S>() => C21<S>();

class C22<T> extends C<T> {}

C22<S> closure22<S>() => C22<S>();

class C23<T> extends C<T> {}

C23<S> closure23<S>() => C23<S>();

class C24<T> extends C<T> {}

C24<S> closure24<S>() => C24<S>();

class C25<T> extends C<T> {}

C25<S> closure25<S>() => C25<S>();

class C26<T> extends C<T> {}

C26<S> closure26<S>() => C26<S>();

class C27<T> extends C<T> {}

C27<S> closure27<S>() => C27<S>();

class C28<T> extends C<T> {}

C28<S> closure28<S>() => C28<S>();

class C29<T> extends C<T> {}

C29<S> closure29<S>() => C29<S>();

class C30<T> extends C<T> {}

C30<S> closure30<S>() => C30<S>();

class C31<T> extends C<T> {}

C31<S> closure31<S>() => C31<S>();

class C32<T> extends C<T> {}

C32<S> closure32<S>() => C32<S>();

class C33<T> extends C<T> {}

C33<S> closure33<S>() => C33<S>();

class C34<T> extends C<T> {}

C34<S> closure34<S>() => C34<S>();

class C35<T> extends C<T> {}

C35<S> closure35<S>() => C35<S>();

class C36<T> extends C<T> {}

C36<S> closure36<S>() => C36<S>();

class C37<T> extends C<T> {}

C37<S> closure37<S>() => C37<S>();

class C38<T> extends C<T> {}

C38<S> closure38<S>() => C38<S>();

class C39<T> extends C<T> {}

C39<S> closure39<S>() => C39<S>();

class C40<T> extends C<T> {}

C40<S> closure40<S>() => C40<S>();

class C41<T> extends C<T> {}

C41<S> closure41<S>() => C41<S>();

class C42<T> extends C<T> {}

C42<S> closure42<S>() => C42<S>();

class C43<T> extends C<T> {}

C43<S> closure43<S>() => C43<S>();

class C44<T> extends C<T> {}

C44<S> closure44<S>() => C44<S>();

class C45<T> extends C<T> {}

C45<S> closure45<S>() => C45<S>();

class C46<T> extends C<T> {}

C46<S> closure46<S>() => C46<S>();

class C47<T> extends C<T> {}

C47<S> closure47<S>() => C47<S>();

class C48<T> extends C<T> {}

C48<S> closure48<S>() => C48<S>();

class C49<T> extends C<T> {}

C49<S> closure49<S>() => C49<S>();

class C50<T> extends C<T> {}

C50<S> closure50<S>() => C50<S>();

class C51<T> extends C<T> {}

C51<S> closure51<S>() => C51<S>();

class C52<T> extends C<T> {}

C52<S> closure52<S>() => C52<S>();

class C53<T> extends C<T> {}

C53<S> closure53<S>() => C53<S>();

class C54<T> extends C<T> {}

C54<S> closure54<S>() => C54<S>();

class C55<T> extends C<T> {}

C55<S> closure55<S>() => C55<S>();

class C56<T> extends C<T> {}

C56<S> closure56<S>() => C56<S>();

class C57<T> extends C<T> {}

C57<S> closure57<S>() => C57<S>();

class C58<T> extends C<T> {}

C58<S> closure58<S>() => C58<S>();

class C59<T> extends C<T> {}

C59<S> closure59<S>() => C59<S>();

class C60<T> extends C<T> {}

C60<S> closure60<S>() => C60<S>();

class C61<T> extends C<T> {}

C61<S> closure61<S>() => C61<S>();

class C62<T> extends C<T> {}

C62<S> closure62<S>() => C62<S>();

class C63<T> extends C<T> {}

C63<S> closure63<S>() => C63<S>();

class C64<T> extends C<T> {}

C64<S> closure64<S>() => C64<S>();

class C65<T> extends C<T> {}

C65<S> closure65<S>() => C65<S>();

class C66<T> extends C<T> {}

C66<S> closure66<S>() => C66<S>();

class C67<T> extends C<T> {}

C67<S> closure67<S>() => C67<S>();

class C68<T> extends C<T> {}

C68<S> closure68<S>() => C68<S>();

class C69<T> extends C<T> {}

C69<S> closure69<S>() => C69<S>();

class C70<T> extends C<T> {}

C70<S> closure70<S>() => C70<S>();

class C71<T> extends C<T> {}

C71<S> closure71<S>() => C71<S>();

class C72<T> extends C<T> {}

C72<S> closure72<S>() => C72<S>();

class C73<T> extends C<T> {}

C73<S> closure73<S>() => C73<S>();

class C74<T> extends C<T> {}

C74<S> closure74<S>() => C74<S>();

class C75<T> extends C<T> {}

C75<S> closure75<S>() => C75<S>();

class C76<T> extends C<T> {}

C76<S> closure76<S>() => C76<S>();

class C77<T> extends C<T> {}

C77<S> closure77<S>() => C77<S>();

class C78<T> extends C<T> {}

C78<S> closure78<S>() => C78<S>();

class C79<T> extends C<T> {}

C79<S> closure79<S>() => C79<S>();

class C80<T> extends C<T> {}

C80<S> closure80<S>() => C80<S>();

class C81<T> extends C<T> {}

C81<S> closure81<S>() => C81<S>();

class C82<T> extends C<T> {}

C82<S> closure82<S>() => C82<S>();

class C83<T> extends C<T> {}

C83<S> closure83<S>() => C83<S>();

class C84<T> extends C<T> {}

C84<S> closure84<S>() => C84<S>();

class C85<T> extends C<T> {}

C85<S> closure85<S>() => C85<S>();

class C86<T> extends C<T> {}

C86<S> closure86<S>() => C86<S>();

class C87<T> extends C<T> {}

C87<S> closure87<S>() => C87<S>();

class C88<T> extends C<T> {}

C88<S> closure88<S>() => C88<S>();

class C89<T> extends C<T> {}

C89<S> closure89<S>() => C89<S>();

class C90<T> extends C<T> {}

C90<S> closure90<S>() => C90<S>();

class C91<T> extends C<T> {}

C91<S> closure91<S>() => C91<S>();

class C92<T> extends C<T> {}

C92<S> closure92<S>() => C92<S>();

class C93<T> extends C<T> {}

C93<S> closure93<S>() => C93<S>();

class C94<T> extends C<T> {}

C94<S> closure94<S>() => C94<S>();

class C95<T> extends C<T> {}

C95<S> closure95<S>() => C95<S>();

class C96<T> extends C<T> {}

C96<S> closure96<S>() => C96<S>();

class C97<T> extends C<T> {}

C97<S> closure97<S>() => C97<S>();

class C98<T> extends C<T> {}

C98<S> closure98<S>() => C98<S>();

class C99<T> extends C<T> {}

C99<S> closure99<S>() => C99<S>();

class C100<T> extends C<T> {}

C100<S> closure100<S>() => C100<S>();

class C101<T> extends C<T> {}

C101<S> closure101<S>() => C101<S>();

class C102<T> extends C<T> {}

C102<S> closure102<S>() => C102<S>();

class C103<T> extends C<T> {}

C103<S> closure103<S>() => C103<S>();

class C104<T> extends C<T> {}

C104<S> closure104<S>() => C104<S>();

class C105<T> extends C<T> {}

C105<S> closure105<S>() => C105<S>();

class C106<T> extends C<T> {}

C106<S> closure106<S>() => C106<S>();

class C107<T> extends C<T> {}

C107<S> closure107<S>() => C107<S>();

class C108<T> extends C<T> {}

C108<S> closure108<S>() => C108<S>();

class C109<T> extends C<T> {}

C109<S> closure109<S>() => C109<S>();

class C110<T> extends C<T> {}

C110<S> closure110<S>() => C110<S>();

class C111<T> extends C<T> {}

C111<S> closure111<S>() => C111<S>();

class C112<T> extends C<T> {}

C112<S> closure112<S>() => C112<S>();

class C113<T> extends C<T> {}

C113<S> closure113<S>() => C113<S>();

class C114<T> extends C<T> {}

C114<S> closure114<S>() => C114<S>();

class C115<T> extends C<T> {}

C115<S> closure115<S>() => C115<S>();

class C116<T> extends C<T> {}

C116<S> closure116<S>() => C116<S>();

class C117<T> extends C<T> {}

C117<S> closure117<S>() => C117<S>();

class C118<T> extends C<T> {}

C118<S> closure118<S>() => C118<S>();

class C119<T> extends C<T> {}

C119<S> closure119<S>() => C119<S>();

class C120<T> extends C<T> {}

C120<S> closure120<S>() => C120<S>();

class C121<T> extends C<T> {}

C121<S> closure121<S>() => C121<S>();

class C122<T> extends C<T> {}

C122<S> closure122<S>() => C122<S>();

class C123<T> extends C<T> {}

C123<S> closure123<S>() => C123<S>();

class C124<T> extends C<T> {}

C124<S> closure124<S>() => C124<S>();

class C125<T> extends C<T> {}

C125<S> closure125<S>() => C125<S>();

class C126<T> extends C<T> {}

C126<S> closure126<S>() => C126<S>();

class C127<T> extends C<T> {}

C127<S> closure127<S>() => C127<S>();

class C128<T> extends C<T> {}

C128<S> closure128<S>() => C128<S>();

class C129<T> extends C<T> {}

C129<S> closure129<S>() => C129<S>();

class C130<T> extends C<T> {}

C130<S> closure130<S>() => C130<S>();

class C131<T> extends C<T> {}

C131<S> closure131<S>() => C131<S>();

class C132<T> extends C<T> {}

C132<S> closure132<S>() => C132<S>();

class C133<T> extends C<T> {}

C133<S> closure133<S>() => C133<S>();

class C134<T> extends C<T> {}

C134<S> closure134<S>() => C134<S>();

class C135<T> extends C<T> {}

C135<S> closure135<S>() => C135<S>();

class C136<T> extends C<T> {}

C136<S> closure136<S>() => C136<S>();

class C137<T> extends C<T> {}

C137<S> closure137<S>() => C137<S>();

class C138<T> extends C<T> {}

C138<S> closure138<S>() => C138<S>();

class C139<T> extends C<T> {}

C139<S> closure139<S>() => C139<S>();

class C140<T> extends C<T> {}

C140<S> closure140<S>() => C140<S>();

class C141<T> extends C<T> {}

C141<S> closure141<S>() => C141<S>();

class C142<T> extends C<T> {}

C142<S> closure142<S>() => C142<S>();

class C143<T> extends C<T> {}

C143<S> closure143<S>() => C143<S>();

class C144<T> extends C<T> {}

C144<S> closure144<S>() => C144<S>();

class C145<T> extends C<T> {}

C145<S> closure145<S>() => C145<S>();

class C146<T> extends C<T> {}

C146<S> closure146<S>() => C146<S>();

class C147<T> extends C<T> {}

C147<S> closure147<S>() => C147<S>();

class C148<T> extends C<T> {}

C148<S> closure148<S>() => C148<S>();

class C149<T> extends C<T> {}

C149<S> closure149<S>() => C149<S>();

class C150<T> extends C<T> {}

C150<S> closure150<S>() => C150<S>();

class C151<T> extends C<T> {}

C151<S> closure151<S>() => C151<S>();

class C152<T> extends C<T> {}

C152<S> closure152<S>() => C152<S>();

class C153<T> extends C<T> {}

C153<S> closure153<S>() => C153<S>();

class C154<T> extends C<T> {}

C154<S> closure154<S>() => C154<S>();

class C155<T> extends C<T> {}

C155<S> closure155<S>() => C155<S>();

class C156<T> extends C<T> {}

C156<S> closure156<S>() => C156<S>();

class C157<T> extends C<T> {}

C157<S> closure157<S>() => C157<S>();

class C158<T> extends C<T> {}

C158<S> closure158<S>() => C158<S>();

class C159<T> extends C<T> {}

C159<S> closure159<S>() => C159<S>();

class C160<T> extends C<T> {}

C160<S> closure160<S>() => C160<S>();

class C161<T> extends C<T> {}

C161<S> closure161<S>() => C161<S>();

class C162<T> extends C<T> {}

C162<S> closure162<S>() => C162<S>();

class C163<T> extends C<T> {}

C163<S> closure163<S>() => C163<S>();

class C164<T> extends C<T> {}

C164<S> closure164<S>() => C164<S>();

class C165<T> extends C<T> {}

C165<S> closure165<S>() => C165<S>();

class C166<T> extends C<T> {}

C166<S> closure166<S>() => C166<S>();

class C167<T> extends C<T> {}

C167<S> closure167<S>() => C167<S>();

class C168<T> extends C<T> {}

C168<S> closure168<S>() => C168<S>();

class C169<T> extends C<T> {}

C169<S> closure169<S>() => C169<S>();

class C170<T> extends C<T> {}

C170<S> closure170<S>() => C170<S>();

class C171<T> extends C<T> {}

C171<S> closure171<S>() => C171<S>();

class C172<T> extends C<T> {}

C172<S> closure172<S>() => C172<S>();

class C173<T> extends C<T> {}

C173<S> closure173<S>() => C173<S>();

class C174<T> extends C<T> {}

C174<S> closure174<S>() => C174<S>();

class C175<T> extends C<T> {}

C175<S> closure175<S>() => C175<S>();

class C176<T> extends C<T> {}

C176<S> closure176<S>() => C176<S>();

class C177<T> extends C<T> {}

C177<S> closure177<S>() => C177<S>();

class C178<T> extends C<T> {}

C178<S> closure178<S>() => C178<S>();

class C179<T> extends C<T> {}

C179<S> closure179<S>() => C179<S>();

class C180<T> extends C<T> {}

C180<S> closure180<S>() => C180<S>();

class C181<T> extends C<T> {}

C181<S> closure181<S>() => C181<S>();

class C182<T> extends C<T> {}

C182<S> closure182<S>() => C182<S>();

class C183<T> extends C<T> {}

C183<S> closure183<S>() => C183<S>();

class C184<T> extends C<T> {}

C184<S> closure184<S>() => C184<S>();

class C185<T> extends C<T> {}

C185<S> closure185<S>() => C185<S>();

class C186<T> extends C<T> {}

C186<S> closure186<S>() => C186<S>();

class C187<T> extends C<T> {}

C187<S> closure187<S>() => C187<S>();

class C188<T> extends C<T> {}

C188<S> closure188<S>() => C188<S>();

class C189<T> extends C<T> {}

C189<S> closure189<S>() => C189<S>();

class C190<T> extends C<T> {}

C190<S> closure190<S>() => C190<S>();

class C191<T> extends C<T> {}

C191<S> closure191<S>() => C191<S>();

class C192<T> extends C<T> {}

C192<S> closure192<S>() => C192<S>();

class C193<T> extends C<T> {}

C193<S> closure193<S>() => C193<S>();

class C194<T> extends C<T> {}

C194<S> closure194<S>() => C194<S>();

class C195<T> extends C<T> {}

C195<S> closure195<S>() => C195<S>();

class C196<T> extends C<T> {}

C196<S> closure196<S>() => C196<S>();

class C197<T> extends C<T> {}

C197<S> closure197<S>() => C197<S>();

class C198<T> extends C<T> {}

C198<S> closure198<S>() => C198<S>();

class C199<T> extends C<T> {}

C199<S> closure199<S>() => C199<S>();

class C200<T> extends C<T> {}

C200<S> closure200<S>() => C200<S>();

class C201<T> extends C<T> {}

C201<S> closure201<S>() => C201<S>();

class C202<T> extends C<T> {}

C202<S> closure202<S>() => C202<S>();

class C203<T> extends C<T> {}

C203<S> closure203<S>() => C203<S>();

class C204<T> extends C<T> {}

C204<S> closure204<S>() => C204<S>();

class C205<T> extends C<T> {}

C205<S> closure205<S>() => C205<S>();

class C206<T> extends C<T> {}

C206<S> closure206<S>() => C206<S>();

class C207<T> extends C<T> {}

C207<S> closure207<S>() => C207<S>();

class C208<T> extends C<T> {}

C208<S> closure208<S>() => C208<S>();

class C209<T> extends C<T> {}

C209<S> closure209<S>() => C209<S>();

class C210<T> extends C<T> {}

C210<S> closure210<S>() => C210<S>();

class C211<T> extends C<T> {}

C211<S> closure211<S>() => C211<S>();

class C212<T> extends C<T> {}

C212<S> closure212<S>() => C212<S>();

class C213<T> extends C<T> {}

C213<S> closure213<S>() => C213<S>();

class C214<T> extends C<T> {}

C214<S> closure214<S>() => C214<S>();

class C215<T> extends C<T> {}

C215<S> closure215<S>() => C215<S>();

class C216<T> extends C<T> {}

C216<S> closure216<S>() => C216<S>();

class C217<T> extends C<T> {}

C217<S> closure217<S>() => C217<S>();

class C218<T> extends C<T> {}

C218<S> closure218<S>() => C218<S>();

class C219<T> extends C<T> {}

C219<S> closure219<S>() => C219<S>();

class C220<T> extends C<T> {}

C220<S> closure220<S>() => C220<S>();

class C221<T> extends C<T> {}

C221<S> closure221<S>() => C221<S>();

class C222<T> extends C<T> {}

C222<S> closure222<S>() => C222<S>();

class C223<T> extends C<T> {}

C223<S> closure223<S>() => C223<S>();

class C224<T> extends C<T> {}

C224<S> closure224<S>() => C224<S>();

class C225<T> extends C<T> {}

C225<S> closure225<S>() => C225<S>();

class C226<T> extends C<T> {}

C226<S> closure226<S>() => C226<S>();

class C227<T> extends C<T> {}

C227<S> closure227<S>() => C227<S>();

class C228<T> extends C<T> {}

C228<S> closure228<S>() => C228<S>();

class C229<T> extends C<T> {}

C229<S> closure229<S>() => C229<S>();

class C230<T> extends C<T> {}

C230<S> closure230<S>() => C230<S>();

class C231<T> extends C<T> {}

C231<S> closure231<S>() => C231<S>();

class C232<T> extends C<T> {}

C232<S> closure232<S>() => C232<S>();

class C233<T> extends C<T> {}

C233<S> closure233<S>() => C233<S>();

class C234<T> extends C<T> {}

C234<S> closure234<S>() => C234<S>();

class C235<T> extends C<T> {}

C235<S> closure235<S>() => C235<S>();

class C236<T> extends C<T> {}

C236<S> closure236<S>() => C236<S>();

class C237<T> extends C<T> {}

C237<S> closure237<S>() => C237<S>();

class C238<T> extends C<T> {}

C238<S> closure238<S>() => C238<S>();

class C239<T> extends C<T> {}

C239<S> closure239<S>() => C239<S>();

class C240<T> extends C<T> {}

C240<S> closure240<S>() => C240<S>();

class C241<T> extends C<T> {}

C241<S> closure241<S>() => C241<S>();

class C242<T> extends C<T> {}

C242<S> closure242<S>() => C242<S>();

class C243<T> extends C<T> {}

C243<S> closure243<S>() => C243<S>();

class C244<T> extends C<T> {}

C244<S> closure244<S>() => C244<S>();

class C245<T> extends C<T> {}

C245<S> closure245<S>() => C245<S>();

class C246<T> extends C<T> {}

C246<S> closure246<S>() => C246<S>();

class C247<T> extends C<T> {}

C247<S> closure247<S>() => C247<S>();

class C248<T> extends C<T> {}

C248<S> closure248<S>() => C248<S>();

class C249<T> extends C<T> {}

C249<S> closure249<S>() => C249<S>();

class C250<T> extends C<T> {}

C250<S> closure250<S>() => C250<S>();

class C251<T> extends C<T> {}

C251<S> closure251<S>() => C251<S>();

class C252<T> extends C<T> {}

C252<S> closure252<S>() => C252<S>();

class C253<T> extends C<T> {}

C253<S> closure253<S>() => C253<S>();

class C254<T> extends C<T> {}

C254<S> closure254<S>() => C254<S>();

class C255<T> extends C<T> {}

C255<S> closure255<S>() => C255<S>();

class C256<T> extends C<T> {}

C256<S> closure256<S>() => C256<S>();

class C257<T> extends C<T> {}

C257<S> closure257<S>() => C257<S>();

class C258<T> extends C<T> {}

C258<S> closure258<S>() => C258<S>();

class C259<T> extends C<T> {}

C259<S> closure259<S>() => C259<S>();

class C260<T> extends C<T> {}

C260<S> closure260<S>() => C260<S>();

class C261<T> extends C<T> {}

C261<S> closure261<S>() => C261<S>();

class C262<T> extends C<T> {}

C262<S> closure262<S>() => C262<S>();

class C263<T> extends C<T> {}

C263<S> closure263<S>() => C263<S>();

class C264<T> extends C<T> {}

C264<S> closure264<S>() => C264<S>();

class C265<T> extends C<T> {}

C265<S> closure265<S>() => C265<S>();

class C266<T> extends C<T> {}

C266<S> closure266<S>() => C266<S>();

class C267<T> extends C<T> {}

C267<S> closure267<S>() => C267<S>();

class C268<T> extends C<T> {}

C268<S> closure268<S>() => C268<S>();

class C269<T> extends C<T> {}

C269<S> closure269<S>() => C269<S>();

class C270<T> extends C<T> {}

C270<S> closure270<S>() => C270<S>();

class C271<T> extends C<T> {}

C271<S> closure271<S>() => C271<S>();

class C272<T> extends C<T> {}

C272<S> closure272<S>() => C272<S>();

class C273<T> extends C<T> {}

C273<S> closure273<S>() => C273<S>();

class C274<T> extends C<T> {}

C274<S> closure274<S>() => C274<S>();

class C275<T> extends C<T> {}

C275<S> closure275<S>() => C275<S>();

class C276<T> extends C<T> {}

C276<S> closure276<S>() => C276<S>();

class C277<T> extends C<T> {}

C277<S> closure277<S>() => C277<S>();

class C278<T> extends C<T> {}

C278<S> closure278<S>() => C278<S>();

class C279<T> extends C<T> {}

C279<S> closure279<S>() => C279<S>();

class C280<T> extends C<T> {}

C280<S> closure280<S>() => C280<S>();

class C281<T> extends C<T> {}

C281<S> closure281<S>() => C281<S>();

class C282<T> extends C<T> {}

C282<S> closure282<S>() => C282<S>();

class C283<T> extends C<T> {}

C283<S> closure283<S>() => C283<S>();

class C284<T> extends C<T> {}

C284<S> closure284<S>() => C284<S>();

class C285<T> extends C<T> {}

C285<S> closure285<S>() => C285<S>();

class C286<T> extends C<T> {}

C286<S> closure286<S>() => C286<S>();

class C287<T> extends C<T> {}

C287<S> closure287<S>() => C287<S>();

class C288<T> extends C<T> {}

C288<S> closure288<S>() => C288<S>();

class C289<T> extends C<T> {}

C289<S> closure289<S>() => C289<S>();

class C290<T> extends C<T> {}

C290<S> closure290<S>() => C290<S>();

class C291<T> extends C<T> {}

C291<S> closure291<S>() => C291<S>();

class C292<T> extends C<T> {}

C292<S> closure292<S>() => C292<S>();

class C293<T> extends C<T> {}

C293<S> closure293<S>() => C293<S>();

class C294<T> extends C<T> {}

C294<S> closure294<S>() => C294<S>();

class C295<T> extends C<T> {}

C295<S> closure295<S>() => C295<S>();

class C296<T> extends C<T> {}

C296<S> closure296<S>() => C296<S>();

class C297<T> extends C<T> {}

C297<S> closure297<S>() => C297<S>();

class C298<T> extends C<T> {}

C298<S> closure298<S>() => C298<S>();

class C299<T> extends C<T> {}

C299<S> closure299<S>() => C299<S>();

class C300<T> extends C<T> {}

C300<S> closure300<S>() => C300<S>();

class C301<T> extends C<T> {}

C301<S> closure301<S>() => C301<S>();

class C302<T> extends C<T> {}

C302<S> closure302<S>() => C302<S>();

class C303<T> extends C<T> {}

C303<S> closure303<S>() => C303<S>();

class C304<T> extends C<T> {}

C304<S> closure304<S>() => C304<S>();

class C305<T> extends C<T> {}

C305<S> closure305<S>() => C305<S>();

class C306<T> extends C<T> {}

C306<S> closure306<S>() => C306<S>();

class C307<T> extends C<T> {}

C307<S> closure307<S>() => C307<S>();

class C308<T> extends C<T> {}

C308<S> closure308<S>() => C308<S>();

class C309<T> extends C<T> {}

C309<S> closure309<S>() => C309<S>();

class C310<T> extends C<T> {}

C310<S> closure310<S>() => C310<S>();

class C311<T> extends C<T> {}

C311<S> closure311<S>() => C311<S>();

class C312<T> extends C<T> {}

C312<S> closure312<S>() => C312<S>();

class C313<T> extends C<T> {}

C313<S> closure313<S>() => C313<S>();

class C314<T> extends C<T> {}

C314<S> closure314<S>() => C314<S>();

class C315<T> extends C<T> {}

C315<S> closure315<S>() => C315<S>();

class C316<T> extends C<T> {}

C316<S> closure316<S>() => C316<S>();

class C317<T> extends C<T> {}

C317<S> closure317<S>() => C317<S>();

class C318<T> extends C<T> {}

C318<S> closure318<S>() => C318<S>();

class C319<T> extends C<T> {}

C319<S> closure319<S>() => C319<S>();

class C320<T> extends C<T> {}

C320<S> closure320<S>() => C320<S>();

class C321<T> extends C<T> {}

C321<S> closure321<S>() => C321<S>();

class C322<T> extends C<T> {}

C322<S> closure322<S>() => C322<S>();

class C323<T> extends C<T> {}

C323<S> closure323<S>() => C323<S>();

class C324<T> extends C<T> {}

C324<S> closure324<S>() => C324<S>();

class C325<T> extends C<T> {}

C325<S> closure325<S>() => C325<S>();

class C326<T> extends C<T> {}

C326<S> closure326<S>() => C326<S>();

class C327<T> extends C<T> {}

C327<S> closure327<S>() => C327<S>();

class C328<T> extends C<T> {}

C328<S> closure328<S>() => C328<S>();

class C329<T> extends C<T> {}

C329<S> closure329<S>() => C329<S>();

class C330<T> extends C<T> {}

C330<S> closure330<S>() => C330<S>();

class C331<T> extends C<T> {}

C331<S> closure331<S>() => C331<S>();

class C332<T> extends C<T> {}

C332<S> closure332<S>() => C332<S>();

class C333<T> extends C<T> {}

C333<S> closure333<S>() => C333<S>();

class C334<T> extends C<T> {}

C334<S> closure334<S>() => C334<S>();

class C335<T> extends C<T> {}

C335<S> closure335<S>() => C335<S>();

class C336<T> extends C<T> {}

C336<S> closure336<S>() => C336<S>();

class C337<T> extends C<T> {}

C337<S> closure337<S>() => C337<S>();

class C338<T> extends C<T> {}

C338<S> closure338<S>() => C338<S>();

class C339<T> extends C<T> {}

C339<S> closure339<S>() => C339<S>();

class C340<T> extends C<T> {}

C340<S> closure340<S>() => C340<S>();

class C341<T> extends C<T> {}

C341<S> closure341<S>() => C341<S>();

class C342<T> extends C<T> {}

C342<S> closure342<S>() => C342<S>();

class C343<T> extends C<T> {}

C343<S> closure343<S>() => C343<S>();

class C344<T> extends C<T> {}

C344<S> closure344<S>() => C344<S>();

class C345<T> extends C<T> {}

C345<S> closure345<S>() => C345<S>();

class C346<T> extends C<T> {}

C346<S> closure346<S>() => C346<S>();

class C347<T> extends C<T> {}

C347<S> closure347<S>() => C347<S>();

class C348<T> extends C<T> {}

C348<S> closure348<S>() => C348<S>();

class C349<T> extends C<T> {}

C349<S> closure349<S>() => C349<S>();

class C350<T> extends C<T> {}

C350<S> closure350<S>() => C350<S>();

class C351<T> extends C<T> {}

C351<S> closure351<S>() => C351<S>();

class C352<T> extends C<T> {}

C352<S> closure352<S>() => C352<S>();

class C353<T> extends C<T> {}

C353<S> closure353<S>() => C353<S>();

class C354<T> extends C<T> {}

C354<S> closure354<S>() => C354<S>();

class C355<T> extends C<T> {}

C355<S> closure355<S>() => C355<S>();

class C356<T> extends C<T> {}

C356<S> closure356<S>() => C356<S>();

class C357<T> extends C<T> {}

C357<S> closure357<S>() => C357<S>();

class C358<T> extends C<T> {}

C358<S> closure358<S>() => C358<S>();

class C359<T> extends C<T> {}

C359<S> closure359<S>() => C359<S>();

class C360<T> extends C<T> {}

C360<S> closure360<S>() => C360<S>();

class C361<T> extends C<T> {}

C361<S> closure361<S>() => C361<S>();

class C362<T> extends C<T> {}

C362<S> closure362<S>() => C362<S>();

class C363<T> extends C<T> {}

C363<S> closure363<S>() => C363<S>();

class C364<T> extends C<T> {}

C364<S> closure364<S>() => C364<S>();

class C365<T> extends C<T> {}

C365<S> closure365<S>() => C365<S>();

class C366<T> extends C<T> {}

C366<S> closure366<S>() => C366<S>();

class C367<T> extends C<T> {}

C367<S> closure367<S>() => C367<S>();

class C368<T> extends C<T> {}

C368<S> closure368<S>() => C368<S>();

class C369<T> extends C<T> {}

C369<S> closure369<S>() => C369<S>();

class C370<T> extends C<T> {}

C370<S> closure370<S>() => C370<S>();

class C371<T> extends C<T> {}

C371<S> closure371<S>() => C371<S>();

class C372<T> extends C<T> {}

C372<S> closure372<S>() => C372<S>();

class C373<T> extends C<T> {}

C373<S> closure373<S>() => C373<S>();

class C374<T> extends C<T> {}

C374<S> closure374<S>() => C374<S>();

class C375<T> extends C<T> {}

C375<S> closure375<S>() => C375<S>();

class C376<T> extends C<T> {}

C376<S> closure376<S>() => C376<S>();

class C377<T> extends C<T> {}

C377<S> closure377<S>() => C377<S>();

class C378<T> extends C<T> {}

C378<S> closure378<S>() => C378<S>();

class C379<T> extends C<T> {}

C379<S> closure379<S>() => C379<S>();

class C380<T> extends C<T> {}

C380<S> closure380<S>() => C380<S>();

class C381<T> extends C<T> {}

C381<S> closure381<S>() => C381<S>();

class C382<T> extends C<T> {}

C382<S> closure382<S>() => C382<S>();

class C383<T> extends C<T> {}

C383<S> closure383<S>() => C383<S>();

class C384<T> extends C<T> {}

C384<S> closure384<S>() => C384<S>();

class C385<T> extends C<T> {}

C385<S> closure385<S>() => C385<S>();

class C386<T> extends C<T> {}

C386<S> closure386<S>() => C386<S>();

class C387<T> extends C<T> {}

C387<S> closure387<S>() => C387<S>();

class C388<T> extends C<T> {}

C388<S> closure388<S>() => C388<S>();

class C389<T> extends C<T> {}

C389<S> closure389<S>() => C389<S>();

class C390<T> extends C<T> {}

C390<S> closure390<S>() => C390<S>();

class C391<T> extends C<T> {}

C391<S> closure391<S>() => C391<S>();

class C392<T> extends C<T> {}

C392<S> closure392<S>() => C392<S>();

class C393<T> extends C<T> {}

C393<S> closure393<S>() => C393<S>();

class C394<T> extends C<T> {}

C394<S> closure394<S>() => C394<S>();

class C395<T> extends C<T> {}

C395<S> closure395<S>() => C395<S>();

class C396<T> extends C<T> {}

C396<S> closure396<S>() => C396<S>();

class C397<T> extends C<T> {}

C397<S> closure397<S>() => C397<S>();

class C398<T> extends C<T> {}

C398<S> closure398<S>() => C398<S>();

class C399<T> extends C<T> {}

C399<S> closure399<S>() => C399<S>();

class C400<T> extends C<T> {}

C400<S> closure400<S>() => C400<S>();

class C401<T> extends C<T> {}

C401<S> closure401<S>() => C401<S>();

class C402<T> extends C<T> {}

C402<S> closure402<S>() => C402<S>();

class C403<T> extends C<T> {}

C403<S> closure403<S>() => C403<S>();

class C404<T> extends C<T> {}

C404<S> closure404<S>() => C404<S>();

class C405<T> extends C<T> {}

C405<S> closure405<S>() => C405<S>();

class C406<T> extends C<T> {}

C406<S> closure406<S>() => C406<S>();

class C407<T> extends C<T> {}

C407<S> closure407<S>() => C407<S>();

class C408<T> extends C<T> {}

C408<S> closure408<S>() => C408<S>();

class C409<T> extends C<T> {}

C409<S> closure409<S>() => C409<S>();

class C410<T> extends C<T> {}

C410<S> closure410<S>() => C410<S>();

class C411<T> extends C<T> {}

C411<S> closure411<S>() => C411<S>();

class C412<T> extends C<T> {}

C412<S> closure412<S>() => C412<S>();

class C413<T> extends C<T> {}

C413<S> closure413<S>() => C413<S>();

class C414<T> extends C<T> {}

C414<S> closure414<S>() => C414<S>();

class C415<T> extends C<T> {}

C415<S> closure415<S>() => C415<S>();

class C416<T> extends C<T> {}

C416<S> closure416<S>() => C416<S>();

class C417<T> extends C<T> {}

C417<S> closure417<S>() => C417<S>();

class C418<T> extends C<T> {}

C418<S> closure418<S>() => C418<S>();

class C419<T> extends C<T> {}

C419<S> closure419<S>() => C419<S>();

class C420<T> extends C<T> {}

C420<S> closure420<S>() => C420<S>();

class C421<T> extends C<T> {}

C421<S> closure421<S>() => C421<S>();

class C422<T> extends C<T> {}

C422<S> closure422<S>() => C422<S>();

class C423<T> extends C<T> {}

C423<S> closure423<S>() => C423<S>();

class C424<T> extends C<T> {}

C424<S> closure424<S>() => C424<S>();

class C425<T> extends C<T> {}

C425<S> closure425<S>() => C425<S>();

class C426<T> extends C<T> {}

C426<S> closure426<S>() => C426<S>();

class C427<T> extends C<T> {}

C427<S> closure427<S>() => C427<S>();

class C428<T> extends C<T> {}

C428<S> closure428<S>() => C428<S>();

class C429<T> extends C<T> {}

C429<S> closure429<S>() => C429<S>();

class C430<T> extends C<T> {}

C430<S> closure430<S>() => C430<S>();

class C431<T> extends C<T> {}

C431<S> closure431<S>() => C431<S>();

class C432<T> extends C<T> {}

C432<S> closure432<S>() => C432<S>();

class C433<T> extends C<T> {}

C433<S> closure433<S>() => C433<S>();

class C434<T> extends C<T> {}

C434<S> closure434<S>() => C434<S>();

class C435<T> extends C<T> {}

C435<S> closure435<S>() => C435<S>();

class C436<T> extends C<T> {}

C436<S> closure436<S>() => C436<S>();

class C437<T> extends C<T> {}

C437<S> closure437<S>() => C437<S>();

class C438<T> extends C<T> {}

C438<S> closure438<S>() => C438<S>();

class C439<T> extends C<T> {}

C439<S> closure439<S>() => C439<S>();

class C440<T> extends C<T> {}

C440<S> closure440<S>() => C440<S>();

class C441<T> extends C<T> {}

C441<S> closure441<S>() => C441<S>();

class C442<T> extends C<T> {}

C442<S> closure442<S>() => C442<S>();

class C443<T> extends C<T> {}

C443<S> closure443<S>() => C443<S>();

class C444<T> extends C<T> {}

C444<S> closure444<S>() => C444<S>();

class C445<T> extends C<T> {}

C445<S> closure445<S>() => C445<S>();

class C446<T> extends C<T> {}

C446<S> closure446<S>() => C446<S>();

class C447<T> extends C<T> {}

C447<S> closure447<S>() => C447<S>();

class C448<T> extends C<T> {}

C448<S> closure448<S>() => C448<S>();

class C449<T> extends C<T> {}

C449<S> closure449<S>() => C449<S>();

class C450<T> extends C<T> {}

C450<S> closure450<S>() => C450<S>();

class C451<T> extends C<T> {}

C451<S> closure451<S>() => C451<S>();

class C452<T> extends C<T> {}

C452<S> closure452<S>() => C452<S>();

class C453<T> extends C<T> {}

C453<S> closure453<S>() => C453<S>();

class C454<T> extends C<T> {}

C454<S> closure454<S>() => C454<S>();

class C455<T> extends C<T> {}

C455<S> closure455<S>() => C455<S>();

class C456<T> extends C<T> {}

C456<S> closure456<S>() => C456<S>();

class C457<T> extends C<T> {}

C457<S> closure457<S>() => C457<S>();

class C458<T> extends C<T> {}

C458<S> closure458<S>() => C458<S>();

class C459<T> extends C<T> {}

C459<S> closure459<S>() => C459<S>();

class C460<T> extends C<T> {}

C460<S> closure460<S>() => C460<S>();

class C461<T> extends C<T> {}

C461<S> closure461<S>() => C461<S>();

class C462<T> extends C<T> {}

C462<S> closure462<S>() => C462<S>();

class C463<T> extends C<T> {}

C463<S> closure463<S>() => C463<S>();

class C464<T> extends C<T> {}

C464<S> closure464<S>() => C464<S>();

class C465<T> extends C<T> {}

C465<S> closure465<S>() => C465<S>();

class C466<T> extends C<T> {}

C466<S> closure466<S>() => C466<S>();

class C467<T> extends C<T> {}

C467<S> closure467<S>() => C467<S>();

class C468<T> extends C<T> {}

C468<S> closure468<S>() => C468<S>();

class C469<T> extends C<T> {}

C469<S> closure469<S>() => C469<S>();

class C470<T> extends C<T> {}

C470<S> closure470<S>() => C470<S>();

class C471<T> extends C<T> {}

C471<S> closure471<S>() => C471<S>();

class C472<T> extends C<T> {}

C472<S> closure472<S>() => C472<S>();

class C473<T> extends C<T> {}

C473<S> closure473<S>() => C473<S>();

class C474<T> extends C<T> {}

C474<S> closure474<S>() => C474<S>();

class C475<T> extends C<T> {}

C475<S> closure475<S>() => C475<S>();

class C476<T> extends C<T> {}

C476<S> closure476<S>() => C476<S>();

class C477<T> extends C<T> {}

C477<S> closure477<S>() => C477<S>();

class C478<T> extends C<T> {}

C478<S> closure478<S>() => C478<S>();

class C479<T> extends C<T> {}

C479<S> closure479<S>() => C479<S>();

class C480<T> extends C<T> {}

C480<S> closure480<S>() => C480<S>();

class C481<T> extends C<T> {}

C481<S> closure481<S>() => C481<S>();

class C482<T> extends C<T> {}

C482<S> closure482<S>() => C482<S>();

class C483<T> extends C<T> {}

C483<S> closure483<S>() => C483<S>();

class C484<T> extends C<T> {}

C484<S> closure484<S>() => C484<S>();

class C485<T> extends C<T> {}

C485<S> closure485<S>() => C485<S>();

class C486<T> extends C<T> {}

C486<S> closure486<S>() => C486<S>();

class C487<T> extends C<T> {}

C487<S> closure487<S>() => C487<S>();

class C488<T> extends C<T> {}

C488<S> closure488<S>() => C488<S>();

class C489<T> extends C<T> {}

C489<S> closure489<S>() => C489<S>();

class C490<T> extends C<T> {}

C490<S> closure490<S>() => C490<S>();

class C491<T> extends C<T> {}

C491<S> closure491<S>() => C491<S>();

class C492<T> extends C<T> {}

C492<S> closure492<S>() => C492<S>();

class C493<T> extends C<T> {}

C493<S> closure493<S>() => C493<S>();

class C494<T> extends C<T> {}

C494<S> closure494<S>() => C494<S>();

class C495<T> extends C<T> {}

C495<S> closure495<S>() => C495<S>();

class C496<T> extends C<T> {}

C496<S> closure496<S>() => C496<S>();

class C497<T> extends C<T> {}

C497<S> closure497<S>() => C497<S>();

class C498<T> extends C<T> {}

C498<S> closure498<S>() => C498<S>();

class C499<T> extends C<T> {}

C499<S> closure499<S>() => C499<S>();

class C500<T> extends C<T> {}

C500<S> closure500<S>() => C500<S>();

class C501<T> extends C<T> {}

C501<S> closure501<S>() => C501<S>();

class C502<T> extends C<T> {}

C502<S> closure502<S>() => C502<S>();

class C503<T> extends C<T> {}

C503<S> closure503<S>() => C503<S>();

class C504<T> extends C<T> {}

C504<S> closure504<S>() => C504<S>();

class C505<T> extends C<T> {}

C505<S> closure505<S>() => C505<S>();

class C506<T> extends C<T> {}

C506<S> closure506<S>() => C506<S>();

class C507<T> extends C<T> {}

C507<S> closure507<S>() => C507<S>();

class C508<T> extends C<T> {}

C508<S> closure508<S>() => C508<S>();

class C509<T> extends C<T> {}

C509<S> closure509<S>() => C509<S>();

class C510<T> extends C<T> {}

C510<S> closure510<S>() => C510<S>();

class C511<T> extends C<T> {}

C511<S> closure511<S>() => C511<S>();

class C512<T> extends C<T> {}

C512<S> closure512<S>() => C512<S>();

class C513<T> extends C<T> {}

C513<S> closure513<S>() => C513<S>();

class C514<T> extends C<T> {}

C514<S> closure514<S>() => C514<S>();

class C515<T> extends C<T> {}

C515<S> closure515<S>() => C515<S>();

class C516<T> extends C<T> {}

C516<S> closure516<S>() => C516<S>();

class C517<T> extends C<T> {}

C517<S> closure517<S>() => C517<S>();

class C518<T> extends C<T> {}

C518<S> closure518<S>() => C518<S>();

class C519<T> extends C<T> {}

C519<S> closure519<S>() => C519<S>();

class C520<T> extends C<T> {}

C520<S> closure520<S>() => C520<S>();

class C521<T> extends C<T> {}

C521<S> closure521<S>() => C521<S>();

class C522<T> extends C<T> {}

C522<S> closure522<S>() => C522<S>();

class C523<T> extends C<T> {}

C523<S> closure523<S>() => C523<S>();

class C524<T> extends C<T> {}

C524<S> closure524<S>() => C524<S>();

class C525<T> extends C<T> {}

C525<S> closure525<S>() => C525<S>();

class C526<T> extends C<T> {}

C526<S> closure526<S>() => C526<S>();

class C527<T> extends C<T> {}

C527<S> closure527<S>() => C527<S>();

class C528<T> extends C<T> {}

C528<S> closure528<S>() => C528<S>();

class C529<T> extends C<T> {}

C529<S> closure529<S>() => C529<S>();

class C530<T> extends C<T> {}

C530<S> closure530<S>() => C530<S>();

class C531<T> extends C<T> {}

C531<S> closure531<S>() => C531<S>();

class C532<T> extends C<T> {}

C532<S> closure532<S>() => C532<S>();

class C533<T> extends C<T> {}

C533<S> closure533<S>() => C533<S>();

class C534<T> extends C<T> {}

C534<S> closure534<S>() => C534<S>();

class C535<T> extends C<T> {}

C535<S> closure535<S>() => C535<S>();

class C536<T> extends C<T> {}

C536<S> closure536<S>() => C536<S>();

class C537<T> extends C<T> {}

C537<S> closure537<S>() => C537<S>();

class C538<T> extends C<T> {}

C538<S> closure538<S>() => C538<S>();

class C539<T> extends C<T> {}

C539<S> closure539<S>() => C539<S>();

class C540<T> extends C<T> {}

C540<S> closure540<S>() => C540<S>();

class C541<T> extends C<T> {}

C541<S> closure541<S>() => C541<S>();

class C542<T> extends C<T> {}

C542<S> closure542<S>() => C542<S>();

class C543<T> extends C<T> {}

C543<S> closure543<S>() => C543<S>();

class C544<T> extends C<T> {}

C544<S> closure544<S>() => C544<S>();

class C545<T> extends C<T> {}

C545<S> closure545<S>() => C545<S>();

class C546<T> extends C<T> {}

C546<S> closure546<S>() => C546<S>();

class C547<T> extends C<T> {}

C547<S> closure547<S>() => C547<S>();

class C548<T> extends C<T> {}

C548<S> closure548<S>() => C548<S>();

class C549<T> extends C<T> {}

C549<S> closure549<S>() => C549<S>();

class C550<T> extends C<T> {}

C550<S> closure550<S>() => C550<S>();

class C551<T> extends C<T> {}

C551<S> closure551<S>() => C551<S>();

class C552<T> extends C<T> {}

C552<S> closure552<S>() => C552<S>();

class C553<T> extends C<T> {}

C553<S> closure553<S>() => C553<S>();

class C554<T> extends C<T> {}

C554<S> closure554<S>() => C554<S>();

class C555<T> extends C<T> {}

C555<S> closure555<S>() => C555<S>();

class C556<T> extends C<T> {}

C556<S> closure556<S>() => C556<S>();

class C557<T> extends C<T> {}

C557<S> closure557<S>() => C557<S>();

class C558<T> extends C<T> {}

C558<S> closure558<S>() => C558<S>();

class C559<T> extends C<T> {}

C559<S> closure559<S>() => C559<S>();

class C560<T> extends C<T> {}

C560<S> closure560<S>() => C560<S>();

class C561<T> extends C<T> {}

C561<S> closure561<S>() => C561<S>();

class C562<T> extends C<T> {}

C562<S> closure562<S>() => C562<S>();

class C563<T> extends C<T> {}

C563<S> closure563<S>() => C563<S>();

class C564<T> extends C<T> {}

C564<S> closure564<S>() => C564<S>();

class C565<T> extends C<T> {}

C565<S> closure565<S>() => C565<S>();

class C566<T> extends C<T> {}

C566<S> closure566<S>() => C566<S>();

class C567<T> extends C<T> {}

C567<S> closure567<S>() => C567<S>();

class C568<T> extends C<T> {}

C568<S> closure568<S>() => C568<S>();

class C569<T> extends C<T> {}

C569<S> closure569<S>() => C569<S>();

class C570<T> extends C<T> {}

C570<S> closure570<S>() => C570<S>();

class C571<T> extends C<T> {}

C571<S> closure571<S>() => C571<S>();

class C572<T> extends C<T> {}

C572<S> closure572<S>() => C572<S>();

class C573<T> extends C<T> {}

C573<S> closure573<S>() => C573<S>();

class C574<T> extends C<T> {}

C574<S> closure574<S>() => C574<S>();

class C575<T> extends C<T> {}

C575<S> closure575<S>() => C575<S>();

class C576<T> extends C<T> {}

C576<S> closure576<S>() => C576<S>();

class C577<T> extends C<T> {}

C577<S> closure577<S>() => C577<S>();

class C578<T> extends C<T> {}

C578<S> closure578<S>() => C578<S>();

class C579<T> extends C<T> {}

C579<S> closure579<S>() => C579<S>();

class C580<T> extends C<T> {}

C580<S> closure580<S>() => C580<S>();

class C581<T> extends C<T> {}

C581<S> closure581<S>() => C581<S>();

class C582<T> extends C<T> {}

C582<S> closure582<S>() => C582<S>();

class C583<T> extends C<T> {}

C583<S> closure583<S>() => C583<S>();

class C584<T> extends C<T> {}

C584<S> closure584<S>() => C584<S>();

class C585<T> extends C<T> {}

C585<S> closure585<S>() => C585<S>();

class C586<T> extends C<T> {}

C586<S> closure586<S>() => C586<S>();

class C587<T> extends C<T> {}

C587<S> closure587<S>() => C587<S>();

class C588<T> extends C<T> {}

C588<S> closure588<S>() => C588<S>();

class C589<T> extends C<T> {}

C589<S> closure589<S>() => C589<S>();

class C590<T> extends C<T> {}

C590<S> closure590<S>() => C590<S>();

class C591<T> extends C<T> {}

C591<S> closure591<S>() => C591<S>();

class C592<T> extends C<T> {}

C592<S> closure592<S>() => C592<S>();

class C593<T> extends C<T> {}

C593<S> closure593<S>() => C593<S>();

class C594<T> extends C<T> {}

C594<S> closure594<S>() => C594<S>();

class C595<T> extends C<T> {}

C595<S> closure595<S>() => C595<S>();

class C596<T> extends C<T> {}

C596<S> closure596<S>() => C596<S>();

class C597<T> extends C<T> {}

C597<S> closure597<S>() => C597<S>();

class C598<T> extends C<T> {}

C598<S> closure598<S>() => C598<S>();

class C599<T> extends C<T> {}

C599<S> closure599<S>() => C599<S>();

class C600<T> extends C<T> {}

C600<S> closure600<S>() => C600<S>();

class C601<T> extends C<T> {}

C601<S> closure601<S>() => C601<S>();

class C602<T> extends C<T> {}

C602<S> closure602<S>() => C602<S>();

class C603<T> extends C<T> {}

C603<S> closure603<S>() => C603<S>();

class C604<T> extends C<T> {}

C604<S> closure604<S>() => C604<S>();

class C605<T> extends C<T> {}

C605<S> closure605<S>() => C605<S>();

class C606<T> extends C<T> {}

C606<S> closure606<S>() => C606<S>();

class C607<T> extends C<T> {}

C607<S> closure607<S>() => C607<S>();

class C608<T> extends C<T> {}

C608<S> closure608<S>() => C608<S>();

class C609<T> extends C<T> {}

C609<S> closure609<S>() => C609<S>();

class C610<T> extends C<T> {}

C610<S> closure610<S>() => C610<S>();

class C611<T> extends C<T> {}

C611<S> closure611<S>() => C611<S>();

class C612<T> extends C<T> {}

C612<S> closure612<S>() => C612<S>();

class C613<T> extends C<T> {}

C613<S> closure613<S>() => C613<S>();

class C614<T> extends C<T> {}

C614<S> closure614<S>() => C614<S>();

class C615<T> extends C<T> {}

C615<S> closure615<S>() => C615<S>();

class C616<T> extends C<T> {}

C616<S> closure616<S>() => C616<S>();

class C617<T> extends C<T> {}

C617<S> closure617<S>() => C617<S>();

class C618<T> extends C<T> {}

C618<S> closure618<S>() => C618<S>();

class C619<T> extends C<T> {}

C619<S> closure619<S>() => C619<S>();

class C620<T> extends C<T> {}

C620<S> closure620<S>() => C620<S>();

class C621<T> extends C<T> {}

C621<S> closure621<S>() => C621<S>();

class C622<T> extends C<T> {}

C622<S> closure622<S>() => C622<S>();

class C623<T> extends C<T> {}

C623<S> closure623<S>() => C623<S>();

class C624<T> extends C<T> {}

C624<S> closure624<S>() => C624<S>();

class C625<T> extends C<T> {}

C625<S> closure625<S>() => C625<S>();

class C626<T> extends C<T> {}

C626<S> closure626<S>() => C626<S>();

class C627<T> extends C<T> {}

C627<S> closure627<S>() => C627<S>();

class C628<T> extends C<T> {}

C628<S> closure628<S>() => C628<S>();

class C629<T> extends C<T> {}

C629<S> closure629<S>() => C629<S>();

class C630<T> extends C<T> {}

C630<S> closure630<S>() => C630<S>();

class C631<T> extends C<T> {}

C631<S> closure631<S>() => C631<S>();

class C632<T> extends C<T> {}

C632<S> closure632<S>() => C632<S>();

class C633<T> extends C<T> {}

C633<S> closure633<S>() => C633<S>();

class C634<T> extends C<T> {}

C634<S> closure634<S>() => C634<S>();

class C635<T> extends C<T> {}

C635<S> closure635<S>() => C635<S>();

class C636<T> extends C<T> {}

C636<S> closure636<S>() => C636<S>();

class C637<T> extends C<T> {}

C637<S> closure637<S>() => C637<S>();

class C638<T> extends C<T> {}

C638<S> closure638<S>() => C638<S>();

class C639<T> extends C<T> {}

C639<S> closure639<S>() => C639<S>();

class C640<T> extends C<T> {}

C640<S> closure640<S>() => C640<S>();

class C641<T> extends C<T> {}

C641<S> closure641<S>() => C641<S>();

class C642<T> extends C<T> {}

C642<S> closure642<S>() => C642<S>();

class C643<T> extends C<T> {}

C643<S> closure643<S>() => C643<S>();

class C644<T> extends C<T> {}

C644<S> closure644<S>() => C644<S>();

class C645<T> extends C<T> {}

C645<S> closure645<S>() => C645<S>();

class C646<T> extends C<T> {}

C646<S> closure646<S>() => C646<S>();

class C647<T> extends C<T> {}

C647<S> closure647<S>() => C647<S>();

class C648<T> extends C<T> {}

C648<S> closure648<S>() => C648<S>();

class C649<T> extends C<T> {}

C649<S> closure649<S>() => C649<S>();

class C650<T> extends C<T> {}

C650<S> closure650<S>() => C650<S>();

class C651<T> extends C<T> {}

C651<S> closure651<S>() => C651<S>();

class C652<T> extends C<T> {}

C652<S> closure652<S>() => C652<S>();

class C653<T> extends C<T> {}

C653<S> closure653<S>() => C653<S>();

class C654<T> extends C<T> {}

C654<S> closure654<S>() => C654<S>();

class C655<T> extends C<T> {}

C655<S> closure655<S>() => C655<S>();

class C656<T> extends C<T> {}

C656<S> closure656<S>() => C656<S>();

class C657<T> extends C<T> {}

C657<S> closure657<S>() => C657<S>();

class C658<T> extends C<T> {}

C658<S> closure658<S>() => C658<S>();

class C659<T> extends C<T> {}

C659<S> closure659<S>() => C659<S>();

class C660<T> extends C<T> {}

C660<S> closure660<S>() => C660<S>();

class C661<T> extends C<T> {}

C661<S> closure661<S>() => C661<S>();

class C662<T> extends C<T> {}

C662<S> closure662<S>() => C662<S>();

class C663<T> extends C<T> {}

C663<S> closure663<S>() => C663<S>();

class C664<T> extends C<T> {}

C664<S> closure664<S>() => C664<S>();

class C665<T> extends C<T> {}

C665<S> closure665<S>() => C665<S>();

class C666<T> extends C<T> {}

C666<S> closure666<S>() => C666<S>();

class C667<T> extends C<T> {}

C667<S> closure667<S>() => C667<S>();

class C668<T> extends C<T> {}

C668<S> closure668<S>() => C668<S>();

class C669<T> extends C<T> {}

C669<S> closure669<S>() => C669<S>();

class C670<T> extends C<T> {}

C670<S> closure670<S>() => C670<S>();

class C671<T> extends C<T> {}

C671<S> closure671<S>() => C671<S>();

class C672<T> extends C<T> {}

C672<S> closure672<S>() => C672<S>();

class C673<T> extends C<T> {}

C673<S> closure673<S>() => C673<S>();

class C674<T> extends C<T> {}

C674<S> closure674<S>() => C674<S>();

class C675<T> extends C<T> {}

C675<S> closure675<S>() => C675<S>();

class C676<T> extends C<T> {}

C676<S> closure676<S>() => C676<S>();

class C677<T> extends C<T> {}

C677<S> closure677<S>() => C677<S>();

class C678<T> extends C<T> {}

C678<S> closure678<S>() => C678<S>();

class C679<T> extends C<T> {}

C679<S> closure679<S>() => C679<S>();

class C680<T> extends C<T> {}

C680<S> closure680<S>() => C680<S>();

class C681<T> extends C<T> {}

C681<S> closure681<S>() => C681<S>();

class C682<T> extends C<T> {}

C682<S> closure682<S>() => C682<S>();

class C683<T> extends C<T> {}

C683<S> closure683<S>() => C683<S>();

class C684<T> extends C<T> {}

C684<S> closure684<S>() => C684<S>();

class C685<T> extends C<T> {}

C685<S> closure685<S>() => C685<S>();

class C686<T> extends C<T> {}

C686<S> closure686<S>() => C686<S>();

class C687<T> extends C<T> {}

C687<S> closure687<S>() => C687<S>();

class C688<T> extends C<T> {}

C688<S> closure688<S>() => C688<S>();

class C689<T> extends C<T> {}

C689<S> closure689<S>() => C689<S>();

class C690<T> extends C<T> {}

C690<S> closure690<S>() => C690<S>();

class C691<T> extends C<T> {}

C691<S> closure691<S>() => C691<S>();

class C692<T> extends C<T> {}

C692<S> closure692<S>() => C692<S>();

class C693<T> extends C<T> {}

C693<S> closure693<S>() => C693<S>();

class C694<T> extends C<T> {}

C694<S> closure694<S>() => C694<S>();

class C695<T> extends C<T> {}

C695<S> closure695<S>() => C695<S>();

class C696<T> extends C<T> {}

C696<S> closure696<S>() => C696<S>();

class C697<T> extends C<T> {}

C697<S> closure697<S>() => C697<S>();

class C698<T> extends C<T> {}

C698<S> closure698<S>() => C698<S>();

class C699<T> extends C<T> {}

C699<S> closure699<S>() => C699<S>();

class C700<T> extends C<T> {}

C700<S> closure700<S>() => C700<S>();

class C701<T> extends C<T> {}

C701<S> closure701<S>() => C701<S>();

class C702<T> extends C<T> {}

C702<S> closure702<S>() => C702<S>();

class C703<T> extends C<T> {}

C703<S> closure703<S>() => C703<S>();

class C704<T> extends C<T> {}

C704<S> closure704<S>() => C704<S>();

class C705<T> extends C<T> {}

C705<S> closure705<S>() => C705<S>();

class C706<T> extends C<T> {}

C706<S> closure706<S>() => C706<S>();

class C707<T> extends C<T> {}

C707<S> closure707<S>() => C707<S>();

class C708<T> extends C<T> {}

C708<S> closure708<S>() => C708<S>();

class C709<T> extends C<T> {}

C709<S> closure709<S>() => C709<S>();

class C710<T> extends C<T> {}

C710<S> closure710<S>() => C710<S>();

class C711<T> extends C<T> {}

C711<S> closure711<S>() => C711<S>();

class C712<T> extends C<T> {}

C712<S> closure712<S>() => C712<S>();

class C713<T> extends C<T> {}

C713<S> closure713<S>() => C713<S>();

class C714<T> extends C<T> {}

C714<S> closure714<S>() => C714<S>();

class C715<T> extends C<T> {}

C715<S> closure715<S>() => C715<S>();

class C716<T> extends C<T> {}

C716<S> closure716<S>() => C716<S>();

class C717<T> extends C<T> {}

C717<S> closure717<S>() => C717<S>();

class C718<T> extends C<T> {}

C718<S> closure718<S>() => C718<S>();

class C719<T> extends C<T> {}

C719<S> closure719<S>() => C719<S>();

class C720<T> extends C<T> {}

C720<S> closure720<S>() => C720<S>();

class C721<T> extends C<T> {}

C721<S> closure721<S>() => C721<S>();

class C722<T> extends C<T> {}

C722<S> closure722<S>() => C722<S>();

class C723<T> extends C<T> {}

C723<S> closure723<S>() => C723<S>();

class C724<T> extends C<T> {}

C724<S> closure724<S>() => C724<S>();

class C725<T> extends C<T> {}

C725<S> closure725<S>() => C725<S>();

class C726<T> extends C<T> {}

C726<S> closure726<S>() => C726<S>();

class C727<T> extends C<T> {}

C727<S> closure727<S>() => C727<S>();

class C728<T> extends C<T> {}

C728<S> closure728<S>() => C728<S>();

class C729<T> extends C<T> {}

C729<S> closure729<S>() => C729<S>();

class C730<T> extends C<T> {}

C730<S> closure730<S>() => C730<S>();

class C731<T> extends C<T> {}

C731<S> closure731<S>() => C731<S>();

class C732<T> extends C<T> {}

C732<S> closure732<S>() => C732<S>();

class C733<T> extends C<T> {}

C733<S> closure733<S>() => C733<S>();

class C734<T> extends C<T> {}

C734<S> closure734<S>() => C734<S>();

class C735<T> extends C<T> {}

C735<S> closure735<S>() => C735<S>();

class C736<T> extends C<T> {}

C736<S> closure736<S>() => C736<S>();

class C737<T> extends C<T> {}

C737<S> closure737<S>() => C737<S>();

class C738<T> extends C<T> {}

C738<S> closure738<S>() => C738<S>();

class C739<T> extends C<T> {}

C739<S> closure739<S>() => C739<S>();

class C740<T> extends C<T> {}

C740<S> closure740<S>() => C740<S>();

class C741<T> extends C<T> {}

C741<S> closure741<S>() => C741<S>();

class C742<T> extends C<T> {}

C742<S> closure742<S>() => C742<S>();

class C743<T> extends C<T> {}

C743<S> closure743<S>() => C743<S>();

class C744<T> extends C<T> {}

C744<S> closure744<S>() => C744<S>();

class C745<T> extends C<T> {}

C745<S> closure745<S>() => C745<S>();

class C746<T> extends C<T> {}

C746<S> closure746<S>() => C746<S>();

class C747<T> extends C<T> {}

C747<S> closure747<S>() => C747<S>();

class C748<T> extends C<T> {}

C748<S> closure748<S>() => C748<S>();

class C749<T> extends C<T> {}

C749<S> closure749<S>() => C749<S>();

class C750<T> extends C<T> {}

C750<S> closure750<S>() => C750<S>();

class C751<T> extends C<T> {}

C751<S> closure751<S>() => C751<S>();

class C752<T> extends C<T> {}

C752<S> closure752<S>() => C752<S>();

class C753<T> extends C<T> {}

C753<S> closure753<S>() => C753<S>();

class C754<T> extends C<T> {}

C754<S> closure754<S>() => C754<S>();

class C755<T> extends C<T> {}

C755<S> closure755<S>() => C755<S>();

class C756<T> extends C<T> {}

C756<S> closure756<S>() => C756<S>();

class C757<T> extends C<T> {}

C757<S> closure757<S>() => C757<S>();

class C758<T> extends C<T> {}

C758<S> closure758<S>() => C758<S>();

class C759<T> extends C<T> {}

C759<S> closure759<S>() => C759<S>();

class C760<T> extends C<T> {}

C760<S> closure760<S>() => C760<S>();

class C761<T> extends C<T> {}

C761<S> closure761<S>() => C761<S>();

class C762<T> extends C<T> {}

C762<S> closure762<S>() => C762<S>();

class C763<T> extends C<T> {}

C763<S> closure763<S>() => C763<S>();

class C764<T> extends C<T> {}

C764<S> closure764<S>() => C764<S>();

class C765<T> extends C<T> {}

C765<S> closure765<S>() => C765<S>();

class C766<T> extends C<T> {}

C766<S> closure766<S>() => C766<S>();

class C767<T> extends C<T> {}

C767<S> closure767<S>() => C767<S>();

class C768<T> extends C<T> {}

C768<S> closure768<S>() => C768<S>();

class C769<T> extends C<T> {}

C769<S> closure769<S>() => C769<S>();

class C770<T> extends C<T> {}

C770<S> closure770<S>() => C770<S>();

class C771<T> extends C<T> {}

C771<S> closure771<S>() => C771<S>();

class C772<T> extends C<T> {}

C772<S> closure772<S>() => C772<S>();

class C773<T> extends C<T> {}

C773<S> closure773<S>() => C773<S>();

class C774<T> extends C<T> {}

C774<S> closure774<S>() => C774<S>();

class C775<T> extends C<T> {}

C775<S> closure775<S>() => C775<S>();

class C776<T> extends C<T> {}

C776<S> closure776<S>() => C776<S>();

class C777<T> extends C<T> {}

C777<S> closure777<S>() => C777<S>();

class C778<T> extends C<T> {}

C778<S> closure778<S>() => C778<S>();

class C779<T> extends C<T> {}

C779<S> closure779<S>() => C779<S>();

class C780<T> extends C<T> {}

C780<S> closure780<S>() => C780<S>();

class C781<T> extends C<T> {}

C781<S> closure781<S>() => C781<S>();

class C782<T> extends C<T> {}

C782<S> closure782<S>() => C782<S>();

class C783<T> extends C<T> {}

C783<S> closure783<S>() => C783<S>();

class C784<T> extends C<T> {}

C784<S> closure784<S>() => C784<S>();

class C785<T> extends C<T> {}

C785<S> closure785<S>() => C785<S>();

class C786<T> extends C<T> {}

C786<S> closure786<S>() => C786<S>();

class C787<T> extends C<T> {}

C787<S> closure787<S>() => C787<S>();

class C788<T> extends C<T> {}

C788<S> closure788<S>() => C788<S>();

class C789<T> extends C<T> {}

C789<S> closure789<S>() => C789<S>();

class C790<T> extends C<T> {}

C790<S> closure790<S>() => C790<S>();

class C791<T> extends C<T> {}

C791<S> closure791<S>() => C791<S>();

class C792<T> extends C<T> {}

C792<S> closure792<S>() => C792<S>();

class C793<T> extends C<T> {}

C793<S> closure793<S>() => C793<S>();

class C794<T> extends C<T> {}

C794<S> closure794<S>() => C794<S>();

class C795<T> extends C<T> {}

C795<S> closure795<S>() => C795<S>();

class C796<T> extends C<T> {}

C796<S> closure796<S>() => C796<S>();

class C797<T> extends C<T> {}

C797<S> closure797<S>() => C797<S>();

class C798<T> extends C<T> {}

C798<S> closure798<S>() => C798<S>();

class C799<T> extends C<T> {}

C799<S> closure799<S>() => C799<S>();

class C800<T> extends C<T> {}

C800<S> closure800<S>() => C800<S>();

class C801<T> extends C<T> {}

C801<S> closure801<S>() => C801<S>();

class C802<T> extends C<T> {}

C802<S> closure802<S>() => C802<S>();

class C803<T> extends C<T> {}

C803<S> closure803<S>() => C803<S>();

class C804<T> extends C<T> {}

C804<S> closure804<S>() => C804<S>();

class C805<T> extends C<T> {}

C805<S> closure805<S>() => C805<S>();

class C806<T> extends C<T> {}

C806<S> closure806<S>() => C806<S>();

class C807<T> extends C<T> {}

C807<S> closure807<S>() => C807<S>();

class C808<T> extends C<T> {}

C808<S> closure808<S>() => C808<S>();

class C809<T> extends C<T> {}

C809<S> closure809<S>() => C809<S>();

class C810<T> extends C<T> {}

C810<S> closure810<S>() => C810<S>();

class C811<T> extends C<T> {}

C811<S> closure811<S>() => C811<S>();

class C812<T> extends C<T> {}

C812<S> closure812<S>() => C812<S>();

class C813<T> extends C<T> {}

C813<S> closure813<S>() => C813<S>();

class C814<T> extends C<T> {}

C814<S> closure814<S>() => C814<S>();

class C815<T> extends C<T> {}

C815<S> closure815<S>() => C815<S>();

class C816<T> extends C<T> {}

C816<S> closure816<S>() => C816<S>();

class C817<T> extends C<T> {}

C817<S> closure817<S>() => C817<S>();

class C818<T> extends C<T> {}

C818<S> closure818<S>() => C818<S>();

class C819<T> extends C<T> {}

C819<S> closure819<S>() => C819<S>();

class C820<T> extends C<T> {}

C820<S> closure820<S>() => C820<S>();

class C821<T> extends C<T> {}

C821<S> closure821<S>() => C821<S>();

class C822<T> extends C<T> {}

C822<S> closure822<S>() => C822<S>();

class C823<T> extends C<T> {}

C823<S> closure823<S>() => C823<S>();

class C824<T> extends C<T> {}

C824<S> closure824<S>() => C824<S>();

class C825<T> extends C<T> {}

C825<S> closure825<S>() => C825<S>();

class C826<T> extends C<T> {}

C826<S> closure826<S>() => C826<S>();

class C827<T> extends C<T> {}

C827<S> closure827<S>() => C827<S>();

class C828<T> extends C<T> {}

C828<S> closure828<S>() => C828<S>();

class C829<T> extends C<T> {}

C829<S> closure829<S>() => C829<S>();

class C830<T> extends C<T> {}

C830<S> closure830<S>() => C830<S>();

class C831<T> extends C<T> {}

C831<S> closure831<S>() => C831<S>();

class C832<T> extends C<T> {}

C832<S> closure832<S>() => C832<S>();

class C833<T> extends C<T> {}

C833<S> closure833<S>() => C833<S>();

class C834<T> extends C<T> {}

C834<S> closure834<S>() => C834<S>();

class C835<T> extends C<T> {}

C835<S> closure835<S>() => C835<S>();

class C836<T> extends C<T> {}

C836<S> closure836<S>() => C836<S>();

class C837<T> extends C<T> {}

C837<S> closure837<S>() => C837<S>();

class C838<T> extends C<T> {}

C838<S> closure838<S>() => C838<S>();

class C839<T> extends C<T> {}

C839<S> closure839<S>() => C839<S>();

class C840<T> extends C<T> {}

C840<S> closure840<S>() => C840<S>();

class C841<T> extends C<T> {}

C841<S> closure841<S>() => C841<S>();

class C842<T> extends C<T> {}

C842<S> closure842<S>() => C842<S>();

class C843<T> extends C<T> {}

C843<S> closure843<S>() => C843<S>();

class C844<T> extends C<T> {}

C844<S> closure844<S>() => C844<S>();

class C845<T> extends C<T> {}

C845<S> closure845<S>() => C845<S>();

class C846<T> extends C<T> {}

C846<S> closure846<S>() => C846<S>();

class C847<T> extends C<T> {}

C847<S> closure847<S>() => C847<S>();

class C848<T> extends C<T> {}

C848<S> closure848<S>() => C848<S>();

class C849<T> extends C<T> {}

C849<S> closure849<S>() => C849<S>();

class C850<T> extends C<T> {}

C850<S> closure850<S>() => C850<S>();

class C851<T> extends C<T> {}

C851<S> closure851<S>() => C851<S>();

class C852<T> extends C<T> {}

C852<S> closure852<S>() => C852<S>();

class C853<T> extends C<T> {}

C853<S> closure853<S>() => C853<S>();

class C854<T> extends C<T> {}

C854<S> closure854<S>() => C854<S>();

class C855<T> extends C<T> {}

C855<S> closure855<S>() => C855<S>();

class C856<T> extends C<T> {}

C856<S> closure856<S>() => C856<S>();

class C857<T> extends C<T> {}

C857<S> closure857<S>() => C857<S>();

class C858<T> extends C<T> {}

C858<S> closure858<S>() => C858<S>();

class C859<T> extends C<T> {}

C859<S> closure859<S>() => C859<S>();

class C860<T> extends C<T> {}

C860<S> closure860<S>() => C860<S>();

class C861<T> extends C<T> {}

C861<S> closure861<S>() => C861<S>();

class C862<T> extends C<T> {}

C862<S> closure862<S>() => C862<S>();

class C863<T> extends C<T> {}

C863<S> closure863<S>() => C863<S>();

class C864<T> extends C<T> {}

C864<S> closure864<S>() => C864<S>();

class C865<T> extends C<T> {}

C865<S> closure865<S>() => C865<S>();

class C866<T> extends C<T> {}

C866<S> closure866<S>() => C866<S>();

class C867<T> extends C<T> {}

C867<S> closure867<S>() => C867<S>();

class C868<T> extends C<T> {}

C868<S> closure868<S>() => C868<S>();

class C869<T> extends C<T> {}

C869<S> closure869<S>() => C869<S>();

class C870<T> extends C<T> {}

C870<S> closure870<S>() => C870<S>();

class C871<T> extends C<T> {}

C871<S> closure871<S>() => C871<S>();

class C872<T> extends C<T> {}

C872<S> closure872<S>() => C872<S>();

class C873<T> extends C<T> {}

C873<S> closure873<S>() => C873<S>();

class C874<T> extends C<T> {}

C874<S> closure874<S>() => C874<S>();

class C875<T> extends C<T> {}

C875<S> closure875<S>() => C875<S>();

class C876<T> extends C<T> {}

C876<S> closure876<S>() => C876<S>();

class C877<T> extends C<T> {}

C877<S> closure877<S>() => C877<S>();

class C878<T> extends C<T> {}

C878<S> closure878<S>() => C878<S>();

class C879<T> extends C<T> {}

C879<S> closure879<S>() => C879<S>();

class C880<T> extends C<T> {}

C880<S> closure880<S>() => C880<S>();

class C881<T> extends C<T> {}

C881<S> closure881<S>() => C881<S>();

class C882<T> extends C<T> {}

C882<S> closure882<S>() => C882<S>();

class C883<T> extends C<T> {}

C883<S> closure883<S>() => C883<S>();

class C884<T> extends C<T> {}

C884<S> closure884<S>() => C884<S>();

class C885<T> extends C<T> {}

C885<S> closure885<S>() => C885<S>();

class C886<T> extends C<T> {}

C886<S> closure886<S>() => C886<S>();

class C887<T> extends C<T> {}

C887<S> closure887<S>() => C887<S>();

class C888<T> extends C<T> {}

C888<S> closure888<S>() => C888<S>();

class C889<T> extends C<T> {}

C889<S> closure889<S>() => C889<S>();

class C890<T> extends C<T> {}

C890<S> closure890<S>() => C890<S>();

class C891<T> extends C<T> {}

C891<S> closure891<S>() => C891<S>();

class C892<T> extends C<T> {}

C892<S> closure892<S>() => C892<S>();

class C893<T> extends C<T> {}

C893<S> closure893<S>() => C893<S>();

class C894<T> extends C<T> {}

C894<S> closure894<S>() => C894<S>();

class C895<T> extends C<T> {}

C895<S> closure895<S>() => C895<S>();

class C896<T> extends C<T> {}

C896<S> closure896<S>() => C896<S>();

class C897<T> extends C<T> {}

C897<S> closure897<S>() => C897<S>();

class C898<T> extends C<T> {}

C898<S> closure898<S>() => C898<S>();

class C899<T> extends C<T> {}

C899<S> closure899<S>() => C899<S>();

class C900<T> extends C<T> {}

C900<S> closure900<S>() => C900<S>();

class C901<T> extends C<T> {}

C901<S> closure901<S>() => C901<S>();

class C902<T> extends C<T> {}

C902<S> closure902<S>() => C902<S>();

class C903<T> extends C<T> {}

C903<S> closure903<S>() => C903<S>();

class C904<T> extends C<T> {}

C904<S> closure904<S>() => C904<S>();

class C905<T> extends C<T> {}

C905<S> closure905<S>() => C905<S>();

class C906<T> extends C<T> {}

C906<S> closure906<S>() => C906<S>();

class C907<T> extends C<T> {}

C907<S> closure907<S>() => C907<S>();

class C908<T> extends C<T> {}

C908<S> closure908<S>() => C908<S>();

class C909<T> extends C<T> {}

C909<S> closure909<S>() => C909<S>();

class C910<T> extends C<T> {}

C910<S> closure910<S>() => C910<S>();

class C911<T> extends C<T> {}

C911<S> closure911<S>() => C911<S>();

class C912<T> extends C<T> {}

C912<S> closure912<S>() => C912<S>();

class C913<T> extends C<T> {}

C913<S> closure913<S>() => C913<S>();

class C914<T> extends C<T> {}

C914<S> closure914<S>() => C914<S>();

class C915<T> extends C<T> {}

C915<S> closure915<S>() => C915<S>();

class C916<T> extends C<T> {}

C916<S> closure916<S>() => C916<S>();

class C917<T> extends C<T> {}

C917<S> closure917<S>() => C917<S>();

class C918<T> extends C<T> {}

C918<S> closure918<S>() => C918<S>();

class C919<T> extends C<T> {}

C919<S> closure919<S>() => C919<S>();

class C920<T> extends C<T> {}

C920<S> closure920<S>() => C920<S>();

class C921<T> extends C<T> {}

C921<S> closure921<S>() => C921<S>();

class C922<T> extends C<T> {}

C922<S> closure922<S>() => C922<S>();

class C923<T> extends C<T> {}

C923<S> closure923<S>() => C923<S>();

class C924<T> extends C<T> {}

C924<S> closure924<S>() => C924<S>();

class C925<T> extends C<T> {}

C925<S> closure925<S>() => C925<S>();

class C926<T> extends C<T> {}

C926<S> closure926<S>() => C926<S>();

class C927<T> extends C<T> {}

C927<S> closure927<S>() => C927<S>();

class C928<T> extends C<T> {}

C928<S> closure928<S>() => C928<S>();

class C929<T> extends C<T> {}

C929<S> closure929<S>() => C929<S>();

class C930<T> extends C<T> {}

C930<S> closure930<S>() => C930<S>();

class C931<T> extends C<T> {}

C931<S> closure931<S>() => C931<S>();

class C932<T> extends C<T> {}

C932<S> closure932<S>() => C932<S>();

class C933<T> extends C<T> {}

C933<S> closure933<S>() => C933<S>();

class C934<T> extends C<T> {}

C934<S> closure934<S>() => C934<S>();

class C935<T> extends C<T> {}

C935<S> closure935<S>() => C935<S>();

class C936<T> extends C<T> {}

C936<S> closure936<S>() => C936<S>();

class C937<T> extends C<T> {}

C937<S> closure937<S>() => C937<S>();

class C938<T> extends C<T> {}

C938<S> closure938<S>() => C938<S>();

class C939<T> extends C<T> {}

C939<S> closure939<S>() => C939<S>();

class C940<T> extends C<T> {}

C940<S> closure940<S>() => C940<S>();

class C941<T> extends C<T> {}

C941<S> closure941<S>() => C941<S>();

class C942<T> extends C<T> {}

C942<S> closure942<S>() => C942<S>();

class C943<T> extends C<T> {}

C943<S> closure943<S>() => C943<S>();

class C944<T> extends C<T> {}

C944<S> closure944<S>() => C944<S>();

class C945<T> extends C<T> {}

C945<S> closure945<S>() => C945<S>();

class C946<T> extends C<T> {}

C946<S> closure946<S>() => C946<S>();

class C947<T> extends C<T> {}

C947<S> closure947<S>() => C947<S>();

class C948<T> extends C<T> {}

C948<S> closure948<S>() => C948<S>();

class C949<T> extends C<T> {}

C949<S> closure949<S>() => C949<S>();

class C950<T> extends C<T> {}

C950<S> closure950<S>() => C950<S>();

class C951<T> extends C<T> {}

C951<S> closure951<S>() => C951<S>();

class C952<T> extends C<T> {}

C952<S> closure952<S>() => C952<S>();

class C953<T> extends C<T> {}

C953<S> closure953<S>() => C953<S>();

class C954<T> extends C<T> {}

C954<S> closure954<S>() => C954<S>();

class C955<T> extends C<T> {}

C955<S> closure955<S>() => C955<S>();

class C956<T> extends C<T> {}

C956<S> closure956<S>() => C956<S>();

class C957<T> extends C<T> {}

C957<S> closure957<S>() => C957<S>();

class C958<T> extends C<T> {}

C958<S> closure958<S>() => C958<S>();

class C959<T> extends C<T> {}

C959<S> closure959<S>() => C959<S>();

class C960<T> extends C<T> {}

C960<S> closure960<S>() => C960<S>();

class C961<T> extends C<T> {}

C961<S> closure961<S>() => C961<S>();

class C962<T> extends C<T> {}

C962<S> closure962<S>() => C962<S>();

class C963<T> extends C<T> {}

C963<S> closure963<S>() => C963<S>();

class C964<T> extends C<T> {}

C964<S> closure964<S>() => C964<S>();

class C965<T> extends C<T> {}

C965<S> closure965<S>() => C965<S>();

class C966<T> extends C<T> {}

C966<S> closure966<S>() => C966<S>();

class C967<T> extends C<T> {}

C967<S> closure967<S>() => C967<S>();

class C968<T> extends C<T> {}

C968<S> closure968<S>() => C968<S>();

class C969<T> extends C<T> {}

C969<S> closure969<S>() => C969<S>();

class C970<T> extends C<T> {}

C970<S> closure970<S>() => C970<S>();

class C971<T> extends C<T> {}

C971<S> closure971<S>() => C971<S>();

class C972<T> extends C<T> {}

C972<S> closure972<S>() => C972<S>();

class C973<T> extends C<T> {}

C973<S> closure973<S>() => C973<S>();

class C974<T> extends C<T> {}

C974<S> closure974<S>() => C974<S>();

class C975<T> extends C<T> {}

C975<S> closure975<S>() => C975<S>();

class C976<T> extends C<T> {}

C976<S> closure976<S>() => C976<S>();

class C977<T> extends C<T> {}

C977<S> closure977<S>() => C977<S>();

class C978<T> extends C<T> {}

C978<S> closure978<S>() => C978<S>();

class C979<T> extends C<T> {}

C979<S> closure979<S>() => C979<S>();

class C980<T> extends C<T> {}

C980<S> closure980<S>() => C980<S>();

class C981<T> extends C<T> {}

C981<S> closure981<S>() => C981<S>();

class C982<T> extends C<T> {}

C982<S> closure982<S>() => C982<S>();

class C983<T> extends C<T> {}

C983<S> closure983<S>() => C983<S>();

class C984<T> extends C<T> {}

C984<S> closure984<S>() => C984<S>();

class C985<T> extends C<T> {}

C985<S> closure985<S>() => C985<S>();

class C986<T> extends C<T> {}

C986<S> closure986<S>() => C986<S>();

class C987<T> extends C<T> {}

C987<S> closure987<S>() => C987<S>();

class C988<T> extends C<T> {}

C988<S> closure988<S>() => C988<S>();

class C989<T> extends C<T> {}

C989<S> closure989<S>() => C989<S>();

class C990<T> extends C<T> {}

C990<S> closure990<S>() => C990<S>();

class C991<T> extends C<T> {}

C991<S> closure991<S>() => C991<S>();

class C992<T> extends C<T> {}

C992<S> closure992<S>() => C992<S>();

class C993<T> extends C<T> {}

C993<S> closure993<S>() => C993<S>();

class C994<T> extends C<T> {}

C994<S> closure994<S>() => C994<S>();

class C995<T> extends C<T> {}

C995<S> closure995<S>() => C995<S>();

class C996<T> extends C<T> {}

C996<S> closure996<S>() => C996<S>();

class C997<T> extends C<T> {}

C997<S> closure997<S>() => C997<S>();

class C998<T> extends C<T> {}

C998<S> closure998<S>() => C998<S>();

class C999<T> extends C<T> {}

C999<S> closure999<S>() => C999<S>();

const instances = <dynamic>[
  closure0<int>,
  closure1<int>,
  closure2<int>,
  closure3<int>,
  closure4<int>,
  closure5<int>,
  closure6<int>,
  closure7<int>,
  closure8<int>,
  closure9<int>,
  closure10<int>,
  closure11<int>,
  closure12<int>,
  closure13<int>,
  closure14<int>,
  closure15<int>,
  closure16<int>,
  closure17<int>,
  closure18<int>,
  closure19<int>,
  closure20<int>,
  closure21<int>,
  closure22<int>,
  closure23<int>,
  closure24<int>,
  closure25<int>,
  closure26<int>,
  closure27<int>,
  closure28<int>,
  closure29<int>,
  closure30<int>,
  closure31<int>,
  closure32<int>,
  closure33<int>,
  closure34<int>,
  closure35<int>,
  closure36<int>,
  closure37<int>,
  closure38<int>,
  closure39<int>,
  closure40<int>,
  closure41<int>,
  closure42<int>,
  closure43<int>,
  closure44<int>,
  closure45<int>,
  closure46<int>,
  closure47<int>,
  closure48<int>,
  closure49<int>,
  closure50<int>,
  closure51<int>,
  closure52<int>,
  closure53<int>,
  closure54<int>,
  closure55<int>,
  closure56<int>,
  closure57<int>,
  closure58<int>,
  closure59<int>,
  closure60<int>,
  closure61<int>,
  closure62<int>,
  closure63<int>,
  closure64<int>,
  closure65<int>,
  closure66<int>,
  closure67<int>,
  closure68<int>,
  closure69<int>,
  closure70<int>,
  closure71<int>,
  closure72<int>,
  closure73<int>,
  closure74<int>,
  closure75<int>,
  closure76<int>,
  closure77<int>,
  closure78<int>,
  closure79<int>,
  closure80<int>,
  closure81<int>,
  closure82<int>,
  closure83<int>,
  closure84<int>,
  closure85<int>,
  closure86<int>,
  closure87<int>,
  closure88<int>,
  closure89<int>,
  closure90<int>,
  closure91<int>,
  closure92<int>,
  closure93<int>,
  closure94<int>,
  closure95<int>,
  closure96<int>,
  closure97<int>,
  closure98<int>,
  closure99<int>,
  closure100<int>,
  closure101<int>,
  closure102<int>,
  closure103<int>,
  closure104<int>,
  closure105<int>,
  closure106<int>,
  closure107<int>,
  closure108<int>,
  closure109<int>,
  closure110<int>,
  closure111<int>,
  closure112<int>,
  closure113<int>,
  closure114<int>,
  closure115<int>,
  closure116<int>,
  closure117<int>,
  closure118<int>,
  closure119<int>,
  closure120<int>,
  closure121<int>,
  closure122<int>,
  closure123<int>,
  closure124<int>,
  closure125<int>,
  closure126<int>,
  closure127<int>,
  closure128<int>,
  closure129<int>,
  closure130<int>,
  closure131<int>,
  closure132<int>,
  closure133<int>,
  closure134<int>,
  closure135<int>,
  closure136<int>,
  closure137<int>,
  closure138<int>,
  closure139<int>,
  closure140<int>,
  closure141<int>,
  closure142<int>,
  closure143<int>,
  closure144<int>,
  closure145<int>,
  closure146<int>,
  closure147<int>,
  closure148<int>,
  closure149<int>,
  closure150<int>,
  closure151<int>,
  closure152<int>,
  closure153<int>,
  closure154<int>,
  closure155<int>,
  closure156<int>,
  closure157<int>,
  closure158<int>,
  closure159<int>,
  closure160<int>,
  closure161<int>,
  closure162<int>,
  closure163<int>,
  closure164<int>,
  closure165<int>,
  closure166<int>,
  closure167<int>,
  closure168<int>,
  closure169<int>,
  closure170<int>,
  closure171<int>,
  closure172<int>,
  closure173<int>,
  closure174<int>,
  closure175<int>,
  closure176<int>,
  closure177<int>,
  closure178<int>,
  closure179<int>,
  closure180<int>,
  closure181<int>,
  closure182<int>,
  closure183<int>,
  closure184<int>,
  closure185<int>,
  closure186<int>,
  closure187<int>,
  closure188<int>,
  closure189<int>,
  closure190<int>,
  closure191<int>,
  closure192<int>,
  closure193<int>,
  closure194<int>,
  closure195<int>,
  closure196<int>,
  closure197<int>,
  closure198<int>,
  closure199<int>,
  closure200<int>,
  closure201<int>,
  closure202<int>,
  closure203<int>,
  closure204<int>,
  closure205<int>,
  closure206<int>,
  closure207<int>,
  closure208<int>,
  closure209<int>,
  closure210<int>,
  closure211<int>,
  closure212<int>,
  closure213<int>,
  closure214<int>,
  closure215<int>,
  closure216<int>,
  closure217<int>,
  closure218<int>,
  closure219<int>,
  closure220<int>,
  closure221<int>,
  closure222<int>,
  closure223<int>,
  closure224<int>,
  closure225<int>,
  closure226<int>,
  closure227<int>,
  closure228<int>,
  closure229<int>,
  closure230<int>,
  closure231<int>,
  closure232<int>,
  closure233<int>,
  closure234<int>,
  closure235<int>,
  closure236<int>,
  closure237<int>,
  closure238<int>,
  closure239<int>,
  closure240<int>,
  closure241<int>,
  closure242<int>,
  closure243<int>,
  closure244<int>,
  closure245<int>,
  closure246<int>,
  closure247<int>,
  closure248<int>,
  closure249<int>,
  closure250<int>,
  closure251<int>,
  closure252<int>,
  closure253<int>,
  closure254<int>,
  closure255<int>,
  closure256<int>,
  closure257<int>,
  closure258<int>,
  closure259<int>,
  closure260<int>,
  closure261<int>,
  closure262<int>,
  closure263<int>,
  closure264<int>,
  closure265<int>,
  closure266<int>,
  closure267<int>,
  closure268<int>,
  closure269<int>,
  closure270<int>,
  closure271<int>,
  closure272<int>,
  closure273<int>,
  closure274<int>,
  closure275<int>,
  closure276<int>,
  closure277<int>,
  closure278<int>,
  closure279<int>,
  closure280<int>,
  closure281<int>,
  closure282<int>,
  closure283<int>,
  closure284<int>,
  closure285<int>,
  closure286<int>,
  closure287<int>,
  closure288<int>,
  closure289<int>,
  closure290<int>,
  closure291<int>,
  closure292<int>,
  closure293<int>,
  closure294<int>,
  closure295<int>,
  closure296<int>,
  closure297<int>,
  closure298<int>,
  closure299<int>,
  closure300<int>,
  closure301<int>,
  closure302<int>,
  closure303<int>,
  closure304<int>,
  closure305<int>,
  closure306<int>,
  closure307<int>,
  closure308<int>,
  closure309<int>,
  closure310<int>,
  closure311<int>,
  closure312<int>,
  closure313<int>,
  closure314<int>,
  closure315<int>,
  closure316<int>,
  closure317<int>,
  closure318<int>,
  closure319<int>,
  closure320<int>,
  closure321<int>,
  closure322<int>,
  closure323<int>,
  closure324<int>,
  closure325<int>,
  closure326<int>,
  closure327<int>,
  closure328<int>,
  closure329<int>,
  closure330<int>,
  closure331<int>,
  closure332<int>,
  closure333<int>,
  closure334<int>,
  closure335<int>,
  closure336<int>,
  closure337<int>,
  closure338<int>,
  closure339<int>,
  closure340<int>,
  closure341<int>,
  closure342<int>,
  closure343<int>,
  closure344<int>,
  closure345<int>,
  closure346<int>,
  closure347<int>,
  closure348<int>,
  closure349<int>,
  closure350<int>,
  closure351<int>,
  closure352<int>,
  closure353<int>,
  closure354<int>,
  closure355<int>,
  closure356<int>,
  closure357<int>,
  closure358<int>,
  closure359<int>,
  closure360<int>,
  closure361<int>,
  closure362<int>,
  closure363<int>,
  closure364<int>,
  closure365<int>,
  closure366<int>,
  closure367<int>,
  closure368<int>,
  closure369<int>,
  closure370<int>,
  closure371<int>,
  closure372<int>,
  closure373<int>,
  closure374<int>,
  closure375<int>,
  closure376<int>,
  closure377<int>,
  closure378<int>,
  closure379<int>,
  closure380<int>,
  closure381<int>,
  closure382<int>,
  closure383<int>,
  closure384<int>,
  closure385<int>,
  closure386<int>,
  closure387<int>,
  closure388<int>,
  closure389<int>,
  closure390<int>,
  closure391<int>,
  closure392<int>,
  closure393<int>,
  closure394<int>,
  closure395<int>,
  closure396<int>,
  closure397<int>,
  closure398<int>,
  closure399<int>,
  closure400<int>,
  closure401<int>,
  closure402<int>,
  closure403<int>,
  closure404<int>,
  closure405<int>,
  closure406<int>,
  closure407<int>,
  closure408<int>,
  closure409<int>,
  closure410<int>,
  closure411<int>,
  closure412<int>,
  closure413<int>,
  closure414<int>,
  closure415<int>,
  closure416<int>,
  closure417<int>,
  closure418<int>,
  closure419<int>,
  closure420<int>,
  closure421<int>,
  closure422<int>,
  closure423<int>,
  closure424<int>,
  closure425<int>,
  closure426<int>,
  closure427<int>,
  closure428<int>,
  closure429<int>,
  closure430<int>,
  closure431<int>,
  closure432<int>,
  closure433<int>,
  closure434<int>,
  closure435<int>,
  closure436<int>,
  closure437<int>,
  closure438<int>,
  closure439<int>,
  closure440<int>,
  closure441<int>,
  closure442<int>,
  closure443<int>,
  closure444<int>,
  closure445<int>,
  closure446<int>,
  closure447<int>,
  closure448<int>,
  closure449<int>,
  closure450<int>,
  closure451<int>,
  closure452<int>,
  closure453<int>,
  closure454<int>,
  closure455<int>,
  closure456<int>,
  closure457<int>,
  closure458<int>,
  closure459<int>,
  closure460<int>,
  closure461<int>,
  closure462<int>,
  closure463<int>,
  closure464<int>,
  closure465<int>,
  closure466<int>,
  closure467<int>,
  closure468<int>,
  closure469<int>,
  closure470<int>,
  closure471<int>,
  closure472<int>,
  closure473<int>,
  closure474<int>,
  closure475<int>,
  closure476<int>,
  closure477<int>,
  closure478<int>,
  closure479<int>,
  closure480<int>,
  closure481<int>,
  closure482<int>,
  closure483<int>,
  closure484<int>,
  closure485<int>,
  closure486<int>,
  closure487<int>,
  closure488<int>,
  closure489<int>,
  closure490<int>,
  closure491<int>,
  closure492<int>,
  closure493<int>,
  closure494<int>,
  closure495<int>,
  closure496<int>,
  closure497<int>,
  closure498<int>,
  closure499<int>,
  closure500<int>,
  closure501<int>,
  closure502<int>,
  closure503<int>,
  closure504<int>,
  closure505<int>,
  closure506<int>,
  closure507<int>,
  closure508<int>,
  closure509<int>,
  closure510<int>,
  closure511<int>,
  closure512<int>,
  closure513<int>,
  closure514<int>,
  closure515<int>,
  closure516<int>,
  closure517<int>,
  closure518<int>,
  closure519<int>,
  closure520<int>,
  closure521<int>,
  closure522<int>,
  closure523<int>,
  closure524<int>,
  closure525<int>,
  closure526<int>,
  closure527<int>,
  closure528<int>,
  closure529<int>,
  closure530<int>,
  closure531<int>,
  closure532<int>,
  closure533<int>,
  closure534<int>,
  closure535<int>,
  closure536<int>,
  closure537<int>,
  closure538<int>,
  closure539<int>,
  closure540<int>,
  closure541<int>,
  closure542<int>,
  closure543<int>,
  closure544<int>,
  closure545<int>,
  closure546<int>,
  closure547<int>,
  closure548<int>,
  closure549<int>,
  closure550<int>,
  closure551<int>,
  closure552<int>,
  closure553<int>,
  closure554<int>,
  closure555<int>,
  closure556<int>,
  closure557<int>,
  closure558<int>,
  closure559<int>,
  closure560<int>,
  closure561<int>,
  closure562<int>,
  closure563<int>,
  closure564<int>,
  closure565<int>,
  closure566<int>,
  closure567<int>,
  closure568<int>,
  closure569<int>,
  closure570<int>,
  closure571<int>,
  closure572<int>,
  closure573<int>,
  closure574<int>,
  closure575<int>,
  closure576<int>,
  closure577<int>,
  closure578<int>,
  closure579<int>,
  closure580<int>,
  closure581<int>,
  closure582<int>,
  closure583<int>,
  closure584<int>,
  closure585<int>,
  closure586<int>,
  closure587<int>,
  closure588<int>,
  closure589<int>,
  closure590<int>,
  closure591<int>,
  closure592<int>,
  closure593<int>,
  closure594<int>,
  closure595<int>,
  closure596<int>,
  closure597<int>,
  closure598<int>,
  closure599<int>,
  closure600<int>,
  closure601<int>,
  closure602<int>,
  closure603<int>,
  closure604<int>,
  closure605<int>,
  closure606<int>,
  closure607<int>,
  closure608<int>,
  closure609<int>,
  closure610<int>,
  closure611<int>,
  closure612<int>,
  closure613<int>,
  closure614<int>,
  closure615<int>,
  closure616<int>,
  closure617<int>,
  closure618<int>,
  closure619<int>,
  closure620<int>,
  closure621<int>,
  closure622<int>,
  closure623<int>,
  closure624<int>,
  closure625<int>,
  closure626<int>,
  closure627<int>,
  closure628<int>,
  closure629<int>,
  closure630<int>,
  closure631<int>,
  closure632<int>,
  closure633<int>,
  closure634<int>,
  closure635<int>,
  closure636<int>,
  closure637<int>,
  closure638<int>,
  closure639<int>,
  closure640<int>,
  closure641<int>,
  closure642<int>,
  closure643<int>,
  closure644<int>,
  closure645<int>,
  closure646<int>,
  closure647<int>,
  closure648<int>,
  closure649<int>,
  closure650<int>,
  closure651<int>,
  closure652<int>,
  closure653<int>,
  closure654<int>,
  closure655<int>,
  closure656<int>,
  closure657<int>,
  closure658<int>,
  closure659<int>,
  closure660<int>,
  closure661<int>,
  closure662<int>,
  closure663<int>,
  closure664<int>,
  closure665<int>,
  closure666<int>,
  closure667<int>,
  closure668<int>,
  closure669<int>,
  closure670<int>,
  closure671<int>,
  closure672<int>,
  closure673<int>,
  closure674<int>,
  closure675<int>,
  closure676<int>,
  closure677<int>,
  closure678<int>,
  closure679<int>,
  closure680<int>,
  closure681<int>,
  closure682<int>,
  closure683<int>,
  closure684<int>,
  closure685<int>,
  closure686<int>,
  closure687<int>,
  closure688<int>,
  closure689<int>,
  closure690<int>,
  closure691<int>,
  closure692<int>,
  closure693<int>,
  closure694<int>,
  closure695<int>,
  closure696<int>,
  closure697<int>,
  closure698<int>,
  closure699<int>,
  closure700<int>,
  closure701<int>,
  closure702<int>,
  closure703<int>,
  closure704<int>,
  closure705<int>,
  closure706<int>,
  closure707<int>,
  closure708<int>,
  closure709<int>,
  closure710<int>,
  closure711<int>,
  closure712<int>,
  closure713<int>,
  closure714<int>,
  closure715<int>,
  closure716<int>,
  closure717<int>,
  closure718<int>,
  closure719<int>,
  closure720<int>,
  closure721<int>,
  closure722<int>,
  closure723<int>,
  closure724<int>,
  closure725<int>,
  closure726<int>,
  closure727<int>,
  closure728<int>,
  closure729<int>,
  closure730<int>,
  closure731<int>,
  closure732<int>,
  closure733<int>,
  closure734<int>,
  closure735<int>,
  closure736<int>,
  closure737<int>,
  closure738<int>,
  closure739<int>,
  closure740<int>,
  closure741<int>,
  closure742<int>,
  closure743<int>,
  closure744<int>,
  closure745<int>,
  closure746<int>,
  closure747<int>,
  closure748<int>,
  closure749<int>,
  closure750<int>,
  closure751<int>,
  closure752<int>,
  closure753<int>,
  closure754<int>,
  closure755<int>,
  closure756<int>,
  closure757<int>,
  closure758<int>,
  closure759<int>,
  closure760<int>,
  closure761<int>,
  closure762<int>,
  closure763<int>,
  closure764<int>,
  closure765<int>,
  closure766<int>,
  closure767<int>,
  closure768<int>,
  closure769<int>,
  closure770<int>,
  closure771<int>,
  closure772<int>,
  closure773<int>,
  closure774<int>,
  closure775<int>,
  closure776<int>,
  closure777<int>,
  closure778<int>,
  closure779<int>,
  closure780<int>,
  closure781<int>,
  closure782<int>,
  closure783<int>,
  closure784<int>,
  closure785<int>,
  closure786<int>,
  closure787<int>,
  closure788<int>,
  closure789<int>,
  closure790<int>,
  closure791<int>,
  closure792<int>,
  closure793<int>,
  closure794<int>,
  closure795<int>,
  closure796<int>,
  closure797<int>,
  closure798<int>,
  closure799<int>,
  closure800<int>,
  closure801<int>,
  closure802<int>,
  closure803<int>,
  closure804<int>,
  closure805<int>,
  closure806<int>,
  closure807<int>,
  closure808<int>,
  closure809<int>,
  closure810<int>,
  closure811<int>,
  closure812<int>,
  closure813<int>,
  closure814<int>,
  closure815<int>,
  closure816<int>,
  closure817<int>,
  closure818<int>,
  closure819<int>,
  closure820<int>,
  closure821<int>,
  closure822<int>,
  closure823<int>,
  closure824<int>,
  closure825<int>,
  closure826<int>,
  closure827<int>,
  closure828<int>,
  closure829<int>,
  closure830<int>,
  closure831<int>,
  closure832<int>,
  closure833<int>,
  closure834<int>,
  closure835<int>,
  closure836<int>,
  closure837<int>,
  closure838<int>,
  closure839<int>,
  closure840<int>,
  closure841<int>,
  closure842<int>,
  closure843<int>,
  closure844<int>,
  closure845<int>,
  closure846<int>,
  closure847<int>,
  closure848<int>,
  closure849<int>,
  closure850<int>,
  closure851<int>,
  closure852<int>,
  closure853<int>,
  closure854<int>,
  closure855<int>,
  closure856<int>,
  closure857<int>,
  closure858<int>,
  closure859<int>,
  closure860<int>,
  closure861<int>,
  closure862<int>,
  closure863<int>,
  closure864<int>,
  closure865<int>,
  closure866<int>,
  closure867<int>,
  closure868<int>,
  closure869<int>,
  closure870<int>,
  closure871<int>,
  closure872<int>,
  closure873<int>,
  closure874<int>,
  closure875<int>,
  closure876<int>,
  closure877<int>,
  closure878<int>,
  closure879<int>,
  closure880<int>,
  closure881<int>,
  closure882<int>,
  closure883<int>,
  closure884<int>,
  closure885<int>,
  closure886<int>,
  closure887<int>,
  closure888<int>,
  closure889<int>,
  closure890<int>,
  closure891<int>,
  closure892<int>,
  closure893<int>,
  closure894<int>,
  closure895<int>,
  closure896<int>,
  closure897<int>,
  closure898<int>,
  closure899<int>,
  closure900<int>,
  closure901<int>,
  closure902<int>,
  closure903<int>,
  closure904<int>,
  closure905<int>,
  closure906<int>,
  closure907<int>,
  closure908<int>,
  closure909<int>,
  closure910<int>,
  closure911<int>,
  closure912<int>,
  closure913<int>,
  closure914<int>,
  closure915<int>,
  closure916<int>,
  closure917<int>,
  closure918<int>,
  closure919<int>,
  closure920<int>,
  closure921<int>,
  closure922<int>,
  closure923<int>,
  closure924<int>,
  closure925<int>,
  closure926<int>,
  closure927<int>,
  closure928<int>,
  closure929<int>,
  closure930<int>,
  closure931<int>,
  closure932<int>,
  closure933<int>,
  closure934<int>,
  closure935<int>,
  closure936<int>,
  closure937<int>,
  closure938<int>,
  closure939<int>,
  closure940<int>,
  closure941<int>,
  closure942<int>,
  closure943<int>,
  closure944<int>,
  closure945<int>,
  closure946<int>,
  closure947<int>,
  closure948<int>,
  closure949<int>,
  closure950<int>,
  closure951<int>,
  closure952<int>,
  closure953<int>,
  closure954<int>,
  closure955<int>,
  closure956<int>,
  closure957<int>,
  closure958<int>,
  closure959<int>,
  closure960<int>,
  closure961<int>,
  closure962<int>,
  closure963<int>,
  closure964<int>,
  closure965<int>,
  closure966<int>,
  closure967<int>,
  closure968<int>,
  closure969<int>,
  closure970<int>,
  closure971<int>,
  closure972<int>,
  closure973<int>,
  closure974<int>,
  closure975<int>,
  closure976<int>,
  closure977<int>,
  closure978<int>,
  closure979<int>,
  closure980<int>,
  closure981<int>,
  closure982<int>,
  closure983<int>,
  closure984<int>,
  closure985<int>,
  closure986<int>,
  closure987<int>,
  closure988<int>,
  closure989<int>,
  closure990<int>,
  closure991<int>,
  closure992<int>,
  closure993<int>,
  closure994<int>,
  closure995<int>,
  closure996<int>,
  closure997<int>,
  closure998<int>,
  closure999<int>,
];
