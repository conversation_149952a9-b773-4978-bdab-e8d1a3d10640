<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-50 -10 2211 5247" style="max-width: 2211px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-f24523f3-b216-44b8-9787-356968831843"><g><rect class="actor actor-bottom" ry="3" rx="3" name="OP" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="1961"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="2036"><tspan dy="0" x="2036">ObjectPool</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="BC" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="1761"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="1836"><tspan dy="0" x="1836">Bytecode</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="F" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="1561"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="1636"><tspan dy="0" x="1636">Function</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="BCD" height="65" width="210" stroke="#666" fill="#eaeaea" y="5161" x="1301"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="1406"><tspan dy="0" x="1406">BytecodeComponentData</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="R" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="1101"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="1176"><tspan dy="0" x="1176">Reader</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="BRH" height="65" width="191" stroke="#666" fill="#eaeaea" y="5161" x="822.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="918"><tspan dy="0" x="918">BytecodeReaderHelper</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="BL" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="340"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="415"><tspan dy="0" x="415">BytecodeLoader</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Client" height="65" width="150" stroke="#666" fill="#eaeaea" y="5161" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="5193.5" x="75"><tspan dy="0" x="75">客户端代码</tspan></text></g><g><line name="OP" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="2036" y1="65" x1="2036" id="actor7"></line><g id="root-7"><rect class="actor actor-top" ry="3" rx="3" name="OP" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1961"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2036"><tspan dy="0" x="2036">ObjectPool</tspan></text></g></g><g><line name="BC" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="1836" y1="65" x1="1836" id="actor6"></line><g id="root-6"><rect class="actor actor-top" ry="3" rx="3" name="BC" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1761"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1836"><tspan dy="0" x="1836">Bytecode</tspan></text></g></g><g><line name="F" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="1636" y1="65" x1="1636" id="actor5"></line><g id="root-5"><rect class="actor actor-top" ry="3" rx="3" name="F" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1561"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1636"><tspan dy="0" x="1636">Function</tspan></text></g></g><g><line name="BCD" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="1406" y1="65" x1="1406" id="actor4"></line><g id="root-4"><rect class="actor actor-top" ry="3" rx="3" name="BCD" height="65" width="210" stroke="#666" fill="#eaeaea" y="0" x="1301"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1406"><tspan dy="0" x="1406">BytecodeComponentData</tspan></text></g></g><g><line name="R" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="1176" y1="65" x1="1176" id="actor3"></line><g id="root-3"><rect class="actor actor-top" ry="3" rx="3" name="R" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1101"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1176"><tspan dy="0" x="1176">Reader</tspan></text></g></g><g><line name="BRH" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="918" y1="65" x1="918" id="actor2"></line><g id="root-2"><rect class="actor actor-top" ry="3" rx="3" name="BRH" height="65" width="191" stroke="#666" fill="#eaeaea" y="0" x="822.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="918"><tspan dy="0" x="918">BytecodeReaderHelper</tspan></text></g></g><g><line name="BL" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="415" y1="65" x1="415" id="actor1"></line><g id="root-1"><rect class="actor actor-top" ry="3" rx="3" name="BL" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="340"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="415"><tspan dy="0" x="415">BytecodeLoader</tspan></text></g></g><g><line name="Client" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="5161" x2="75" y1="65" x1="75" id="actor0"></line><g id="root-0"><rect class="actor actor-top" ry="3" rx="3" name="Client" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="75"><tspan dy="0" x="75">客户端代码</tspan></text></g></g><style>#mermaid-f24523f3-b216-44b8-9787-356968831843{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .error-icon{fill:#a44141;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-thickness-normal{stroke-width:1px;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .marker.cross{stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-f24523f3-b216-44b8-9787-356968831843 p{margin:0;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actor{stroke:#ccc;fill:#1f2020;}#mermaid-f24523f3-b216-44b8-9787-356968831843 text.actor&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actor-line{stroke:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 #arrowhead path{fill:lightgrey;stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .sequenceNumber{fill:black;}#mermaid-f24523f3-b216-44b8-9787-356968831843 #sequencenumber{fill:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 #crosshead path{fill:lightgrey;stroke:lightgrey;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .messageText{fill:lightgrey;stroke:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .labelBox{stroke:#ccc;fill:#1f2020;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .labelText,#mermaid-f24523f3-b216-44b8-9787-356968831843 .labelText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .loopText,#mermaid-f24523f3-b216-44b8-9787-356968831843 .loopText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:#ccc;fill:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .note{stroke:hsl(180, 0%, 18.3529411765%);fill:hsl(180, 1.5873015873%, 28.3529411765%);}#mermaid-f24523f3-b216-44b8-9787-356968831843 .noteText,#mermaid-f24523f3-b216-44b8-9787-356968831843 .noteText&gt;tspan{fill:rgb(183.8476190475, 181.5523809523, 181.5523809523);stroke:none;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .activation0{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .activation1{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .activation2{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actorPopupMenu{position:absolute;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actorPopupMenuPanel{position:absolute;fill:#1f2020;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actor-man line{stroke:#ccc;fill:#1f2020;}#mermaid-f24523f3-b216-44b8-9787-356968831843 .actor-man circle,#mermaid-f24523f3-b216-44b8-9787-356968831843 line{stroke:#ccc;fill:#1f2020;stroke-width:2px;}#mermaid-f24523f3-b216-44b8-9787-356968831843 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><symbol height="24" width="24" id="computer"><path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"></path></symbol></defs><defs><symbol clip-rule="evenodd" fill-rule="evenodd" id="database"><path d="M12.258.001l.256.004.255.005.253.008.251.01.249.012.247.015.246.016.242.019.241.02.239.023.236.024.233.027.231.028.229.031.225.032.223.034.22.036.217.038.214.04.211.041.208.043.205.045.201.046.198.048.194.05.191.051.187.053.183.054.18.056.175.057.172.059.168.06.163.061.16.063.155.064.15.066.074.033.073.033.071.034.07.034.069.035.068.035.067.035.066.035.064.036.064.036.062.036.06.036.06.037.058.037.058.037.055.038.055.038.053.038.052.038.051.039.05.039.048.039.047.039.045.04.044.04.043.04.041.04.04.041.039.041.037.041.036.041.034.041.033.042.032.042.03.042.029.042.027.042.026.043.024.043.023.043.021.043.02.043.018.044.017.043.015.044.013.044.012.044.011.045.009.044.007.045.006.045.004.045.002.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z" transform="scale(.5)"></path></symbol></defs><defs><symbol height="24" width="24" id="clock"><path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"></path></symbol></defs><defs><marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead"><path d="M -1 0 L 10 5 L 0 10 z"></path></marker></defs><defs><marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead"><path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber"><circle r="6" cy="15" cx="15"></circle></marker></defs><g><rect class="activation0" height="220" width="10" stroke="#666" fill="#EDF2AE" y="121" x="410"></rect></g><g><rect class="activation0" height="2385" width="10" stroke="#666" fill="#EDF2AE" y="399" x="410"></rect></g><g><rect class="note" height="39" width="218" stroke="#666" fill="#EDF2AE" y="407" x="306"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="412" x="415"><tspan x="415">Step 1: 读取 bytecode 组件</tspan></text></g><g><rect class="activation0" height="166" width="10" stroke="#666" fill="#EDF2AE" y="504" x="913"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="560" x="1171"></rect></g><g><rect class="activation0" height="936" width="10" stroke="#666" fill="#EDF2AE" y="728" x="913"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="784" x="1171"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="974" x="1171"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="1209" x="1171"></rect></g><g><line class="loopLine" y2="1116" x2="1191" y1="1116" x1="903"></line><line class="loopLine" y2="1273" x2="1191" y1="1116" x1="1191"></line><line class="loopLine" y2="1273" x2="1191" y1="1273" x1="903"></line><line class="loopLine" y2="1273" x2="903" y1="1116" x1="903"></line><polygon class="labelBox" points="903,1116 953,1116 953,1129 944.6,1136 903,1136"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1129" x="928">loop</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1134" x="1072"><tspan x="1072">[读取各段偏移]</tspan></text></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="1331" x="1401"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="1488" x="1171"></rect></g><g><line class="loopLine" y2="1395" x2="1417" y1="1395" x1="903"></line><line class="loopLine" y2="1608" x2="1417" y1="1395" x1="1417"></line><line class="loopLine" y2="1608" x2="1417" y1="1608" x1="903"></line><line class="loopLine" y2="1608" x2="903" y1="1395" x1="903"></line><polygon class="labelBox" points="903,1395 953,1395 953,1408 944.6,1415 903,1415"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1408" x="928">loop</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1413" x="1185"><tspan x="1185">[读取对象偏移]</tspan></text></g><g><rect class="note" height="39" width="191" stroke="#666" fill="#EDF2AE" y="1674" x="319.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1679" x="415"><tspan x="415">Step 2: 初始化组件数据</tspan></text></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="1771" x="913"></rect></g><g><rect class="note" height="39" width="159" stroke="#666" fill="#EDF2AE" y="1835" x="335.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1840" x="415"><tspan x="415">Step 3: 读取库声明</tspan></text></g><g><rect class="activation0" height="389" width="10" stroke="#666" fill="#EDF2AE" y="1932" x="913"></rect></g><g><line class="loopLine" y2="1940" x2="1025.5" y1="1940" x1="820.5"></line><line class="loopLine" y2="2273" x2="1025.5" y1="1940" x1="1025.5"></line><line class="loopLine" y2="2273" x2="1025.5" y1="2273" x1="820.5"></line><line class="loopLine" y2="2273" x2="820.5" y1="1940" x1="820.5"></line><polygon class="labelBox" points="820.5,1940 870.5,1940 870.5,1953 862.1,1960 820.5,1960"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1953" x="846">loop</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1958" x="948"><tspan x="948">[处理每个库]</tspan></text></g><g><rect class="note" height="39" width="159" stroke="#666" fill="#EDF2AE" y="2331" x="335.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2336" x="415"><tspan x="415">Step 4: 处理主函数</tspan></text></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="2503" x="913"></rect></g><g><line class="loopLine" y2="2380" x2="1031.5" y1="2380" x1="331"></line><line class="loopLine" y2="2728" x2="1031.5" y1="2380" x1="1031.5"></line><line class="loopLine" y2="2728" x2="1031.5" y1="2728" x1="331"></line><line class="loopLine" y2="2728" x2="331" y1="2380" x1="331"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="2572" x2="1031.5" y1="2572" x1="331"></line><polygon class="labelBox" points="331,2380 381,2380 381,2393 372.6,2400 331,2400"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2393" x="356">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2398" x="706.25"><tspan x="706.25">[有主函数]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2590" x="681.25">[无主函数]</text></g><g><rect class="note" height="39" width="2011" stroke="#666" fill="#EDF2AE" y="2794" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2799" x="1056"><tspan x="1056">后续按需加载函数字节码</tspan></text></g><g><rect class="activation0" height="2250" width="10" stroke="#666" fill="#EDF2AE" y="2891" x="913"></rect></g><g><rect class="note" height="39" width="191" stroke="#666" fill="#EDF2AE" y="2899" x="822.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2904" x="918"><tspan x="918">读取代码标志</tspan></text></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="2996" x="1171"></rect></g><g><rect class="note" height="39" width="191" stroke="#666" fill="#EDF2AE" y="3138" x="822.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3143" x="918"><tspan x="918">读取常量池</tspan></text></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="3235" x="2031"></rect></g><g><rect class="activation1" height="936" width="10" stroke="#666" fill="#EDF2AE" y="3377" x="918"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="3478" x="1171"></rect></g><g><line class="loopLine" y2="3542" x2="2047" y1="3542" x1="799.5"></line><line class="loopLine" y2="4169" x2="2047" y1="3542" x1="2047"></line><line class="loopLine" y2="4169" x2="2047" y1="4169" x1="799.5"></line><line class="loopLine" y2="4169" x2="799.5" y1="3542" x1="799.5"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="3681" x2="2047" y1="3681" x1="799.5"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="3924" x2="2047" y1="3924" x1="799.5"></line><polygon class="labelBox" points="799.5,3542 849.5,3542 849.5,3555 841.1,3562 799.5,3562"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3555" x="825">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="3560" x="1448.25"><tspan x="1448.25">[StaticField]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="3699" x="1423.25">[InstanceField]</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="3942" x="1423.25">[DirectCall]</text></g><g><line class="loopLine" y2="3385" x2="2057" y1="3385" x1="789.5"></line><line class="loopLine" y2="4235" x2="2057" y1="3385" x1="2057"></line><line class="loopLine" y2="4235" x2="2057" y1="4235" x1="789.5"></line><line class="loopLine" y2="4235" x2="789.5" y1="3385" x1="789.5"></line><polygon class="labelBox" points="789.5,3385 839.5,3385 839.5,3398 831.1,3405 789.5,3405"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3398" x="815">loop</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="3403" x="1448.25"><tspan x="1448.25">[处理每个常量池条目]</tspan></text></g><g><rect class="note" height="39" width="191" stroke="#666" fill="#EDF2AE" y="4323" x="822.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4328" x="918"><tspan x="918">读取字节码</tspan></text></g><g><rect class="activation0" height="46" width="10" stroke="#666" fill="#EDF2AE" y="4420" x="1171"></rect></g><g><rect class="activation0" height="46" width="10" stroke="#666" fill="#EDF2AE" y="4524" x="1171"></rect></g><g><rect class="activation0" height="54" width="10" stroke="#666" fill="#EDF2AE" y="4628" x="1831"></rect></g><g><rect class="activation0" height="46" width="10" stroke="#666" fill="#EDF2AE" y="4740" x="1631"></rect></g><g><rect class="note" height="39" width="191" stroke="#666" fill="#EDF2AE" y="4796" x="822.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4801" x="918"><tspan x="918">读取元数据</tspan></text></g><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="80" x="242">new BytecodeLoader(thread, binary)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="121" x2="407" y1="121" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="136" x="420">初始化成员变量</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 420,169 C 480,159 480,199 420,189"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="214" x="420">设置线程的 bytecode_loader</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 420,255 C 480,245 480,285 420,275"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="300" x="245">BytecodeLoader 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="341" x2="79" y1="341" x1="410"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="356" x="242">LoadBytecode()</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="397" x2="407" y1="397" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="461" x="665">new BytecodeReaderHelper(thread, binary)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="502" x2="910" y1="502" x1="420"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="517" x="1046">new Reader(binary)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="558" x2="1168" y1="558" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="573" x="1049">Reader 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="614" x2="926" y1="614" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="629" x="668">BytecodeReaderHelper 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="670" x2="423" y1="670" x1="913"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="685" x="665">ReadBytecodeComponent()</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="726" x2="910" y1="726" x1="420"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="741" x="1046">ReadUInt32() // 读取魔数</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="782" x2="1168" y1="782" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="797" x="1049">magic</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="838" x2="926" y1="838" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="853" x="923">验证魔数</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,886 C 983,876 983,916 923,906"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="931" x="1046">ReadUInt32() // 读取版本</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="972" x2="1168" y1="972" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="987" x="1049">version</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1028" x2="926" y1="1028" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1043" x="923">验证版本</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,1076 C 983,1066 983,1106 923,1096"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1166" x="1046">ReadUInt32()</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1207" x2="1168" y1="1207" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1222" x="1049">offset</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1263" x2="926" y1="1263" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1288" x="1161">New(...) // 创建组件数据</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1329" x2="1398" y1="1329" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1344" x="1164">BytecodeComponentData</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1385" x2="926" y1="1385" x1="1401"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1445" x="1046">ReadUInt()</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1486" x2="1168" y1="1486" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1501" x="1049">object_offset</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1542" x2="926" y1="1542" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1557" x="1163">SetObject(index, offset)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1598" x2="1402" y1="1598" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1623" x="668">bytecode_component_array</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1664" x2="423" y1="1664" x1="913"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1728" x="665">new BytecodeReaderHelper(thread, bytecode_component)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1769" x2="910" y1="1769" x1="420"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1784" x="668">BytecodeReaderHelper 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1825" x2="423" y1="1825" x1="913"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1889" x="665">ReadLibraryDeclarations(num_libraries)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1930" x2="910" y1="1930" x1="420"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1990" x="923">ReadLibraryDeclaration()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,2031 C 983,2021 983,2061 923,2051"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2076" x="923">ReadClassDeclaration()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,2117 C 983,2107 983,2147 923,2137"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2162" x="923">ReadMembers()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,2203 C 983,2193 983,2233 923,2223"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2288" x="668">完成</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2321" x2="423" y1="2321" x1="913"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2430" x="919">ReadObject() // 读取主函数</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 919,2471 C 979,2461 979,2501 919,2491"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2516" x="668">main_function</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2557" x2="423" y1="2557" x1="913"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2617" x="420">return Function::null()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 420,2658 C 480,2648 480,2688 420,2678"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2743" x="245">main_function</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2784" x2="79" y1="2784" x1="410"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2848" x="493">ReadCode(function, code_offset)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2889" x2="910" y1="2889" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2953" x="1046">ReadUInt()</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2994" x2="1168" y1="2994" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3009" x="1049">flags</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3050" x2="926" y1="3050" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3065" x="923">解析各种标志位</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,3098 C 983,3088 983,3128 923,3118"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3192" x="1476">new ObjectPool(obj_count)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3233" x2="2028" y1="3233" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3248" x="1479">ObjectPool 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3289" x2="926" y1="3289" x1="2031"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3304" x="923">ReadConstantPool(function, pool, 0)</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,3345 C 983,3335 983,3375 923,3365"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3435" x="1048">ReadByte() // 读取标签</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3476" x2="1168" y1="3476" x1="928"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3491" x="1051">tag</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3532" x2="931" y1="3532" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3595" x="928">ReadObject() // 读取字段</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 928,3636 C 988,3626 988,3666 928,3656"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3726" x="928">ReadObject() // 读取字段</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 928,3767 C 988,3757 988,3797 928,3787"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3812" x="1480">SetObjectAt(i, field_offset)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3853" x2="2032" y1="3853" x1="928"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3868" x="1480">SetObjectAt(i+1, field)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3909" x2="2032" y1="3909" x1="928"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3972" x="928">ReadObject() // 读取目标函数</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 928,4013 C 988,4003 988,4043 928,4033"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4058" x="928">ReadObject() // 读取参数描述符</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 928,4099 C 988,4089 988,4129 928,4119"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4184" x="1480">SetObjectAt(i, obj)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="4225" x2="2032" y1="4225" x1="928"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4250" x="928">完成常量池</text><path style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" d="M 928,4283 C 988,4273 988,4313 928,4303"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4377" x="1046">ReadUInt() // 读取大小</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="4418" x2="1168" y1="4418" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4433" x="1049">size</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="4466" x2="926" y1="4466" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4481" x="1046">BufferAt(offset)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="4522" x2="1168" y1="4522" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4537" x="1049">data_ptr</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="4570" x2="926" y1="4570" x1="1171"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4585" x="1376">New(data_ptr, size, offset, binary, pool)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="4626" x2="1828" y1="4626" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4641" x="1379">Bytecode 实例</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="4682" x2="926" y1="4682" x1="1831"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4697" x="1276">AttachBytecode(bytecode)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="4738" x2="1628" y1="4738" x1="923"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4753" x="1279">完成</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="4786" x2="926" y1="4786" x1="1631"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4850" x="923">ReadExceptionsTable()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,4891 C 983,4881 983,4921 923,4911"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="4936" x="923">ReadSourcePositions()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,4977 C 983,4967 983,5007 923,4997"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="5022" x="923">ReadLocalVariables()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 923,5063 C 983,5053 983,5093 923,5083"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="5108" x="496">完成</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="5141" x2="79" y1="5141" x1="913"></line></svg>