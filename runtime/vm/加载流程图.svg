<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 3528.076171875 2796.6875" style="max-width: 3528.076171875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689"><style>#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .error-icon{fill:#a44141;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-thickness-normal{stroke-width:1px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .marker.cross{stroke:lightgrey;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 p{margin:0;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster-label text{fill:#F9FFFE;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster-label span{color:#F9FFFE;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster-label span p{background-color:transparent;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .label text,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 span{fill:#ccc;color:#ccc;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node rect,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node circle,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node ellipse,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node polygon,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .rough-node .label text,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node .label text,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .image-shape .label,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .icon-shape .label{text-anchor:middle;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .rough-node .label,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node .label,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .image-shape .label,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .icon-shape .label{text-align:center;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .node.clickable{cursor:pointer;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .arrowheadPath{fill:lightgrey;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster text{fill:#F9FFFE;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .cluster span{color:#F9FFFE;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 rect.text{fill:none;stroke-width:0;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .icon-shape,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .icon-shape p,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .icon-shape rect,#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M3049.348,809L3049.348,937.667C3049.348,1066.333,3049.348,1323.667,3049.348,1455.833C3049.348,1588,3049.348,1595,3049.348,1598.5L3049.348,1602"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M3049.348,1660L3049.348,1664.167C3049.348,1668.333,3049.348,1676.667,3049.348,1684.333C3049.348,1692,3049.348,1699,3049.348,1702.5L3049.348,1706"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M2942.852,1745.782L2855.507,1752.985C2768.163,1760.188,2593.475,1774.594,2506.131,1785.297C2418.787,1796,2418.787,1803,2418.787,1806.5L2418.787,1810"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D1_3" d="M2418.787,1892L2418.787,1896.167C2418.787,1900.333,2418.787,1908.667,2418.787,1926.391C2418.787,1944.115,2418.787,1971.229,2418.787,1984.786L2418.787,1998.344"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D2_4" d="M2418.787,2056.344L2418.787,2072.568C2418.787,2088.792,2418.787,2121.24,2418.787,2142.964C2418.787,2164.688,2418.787,2175.688,2418.787,2181.188L2418.787,2186.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D3_5" d="M2418.787,2244.688L2418.787,2248.854C2418.787,2253.021,2418.787,2261.354,2418.787,2269.021C2418.787,2276.688,2418.787,2283.688,2418.787,2287.188L2418.787,2290.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_D4_6" d="M2418.787,2348.688L2418.787,2352.854C2418.787,2357.021,2418.787,2365.354,2418.787,2373.021C2418.787,2380.688,2418.787,2387.688,2418.787,2391.188L2418.787,2394.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_7" d="M2942.852,1753.444L2904.473,1759.37C2866.094,1765.296,2789.337,1777.148,2750.959,1788.574C2712.58,1800,2712.58,1811,2712.58,1816.5L2712.58,1822"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E1_8" d="M2712.58,1880L2712.58,1886.167C2712.58,1892.333,2712.58,1904.667,2712.58,1922.391C2712.58,1940.115,2712.58,1963.229,2712.58,1974.786L2712.58,1986.344"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_E2_9" d="M2712.58,2068.344L2712.58,2082.568C2712.58,2096.792,2712.58,2125.24,2712.58,2144.964C2712.58,2164.688,2712.58,2175.688,2712.58,2181.188L2712.58,2186.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_F_10" d="M3027.034,1764L3023.59,1768.167C3020.147,1772.333,3013.26,1780.667,3009.817,1788.333C3006.373,1796,3006.373,1803,3006.373,1806.5L3006.373,1810"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_F1_11" d="M3006.373,1892L3006.373,1896.167C3006.373,1900.333,3006.373,1908.667,3006.373,1926.391C3006.373,1944.115,3006.373,1971.229,3006.373,1984.786L3006.373,1998.344"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_F3_12" d="M3006.373,2244.688L3006.373,2248.854C3006.373,2253.021,3006.373,2261.354,3006.373,2269.021C3006.373,2276.688,3006.373,2283.688,3006.373,2287.188L3006.373,2290.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_F4_13" d="M3006.373,2348.688L3006.373,2352.854C3006.373,2357.021,3006.373,2365.354,3006.373,2375.021C3006.373,2384.688,3006.373,2395.688,3006.373,2401.188L3006.373,2406.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F4_F5_14" d="M3006.373,2464.688L3006.373,2470.854C3006.373,2477.021,3006.373,2489.354,3006.373,2499.021C3006.373,2508.688,3006.373,2515.688,3006.373,2519.188L3006.373,2522.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F5_F6_15" d="M3006.373,2580.688L3006.373,2584.854C3006.373,2589.021,3006.373,2597.354,3006.373,2605.021C3006.373,2612.688,3006.373,2619.688,3006.373,2623.188L3006.373,2626.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F6_F7_16" d="M3006.373,2684.688L3006.373,2688.854C3006.373,2693.021,3006.373,2701.354,3006.373,2709.021C3006.373,2716.688,3006.373,2723.688,3006.373,2727.188L3006.373,2730.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_G_17" d="M3155.844,1756.377L3185.727,1761.814C3215.609,1767.251,3275.375,1778.126,3305.258,1789.063C3335.141,1800,3335.141,1811,3335.141,1816.5L3335.141,1822"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_G1_18" d="M3335.141,1880L3335.141,1886.167C3335.141,1892.333,3335.141,1904.667,3335.211,1914.417C3335.281,1924.167,3335.422,1931.334,3335.492,1934.917L3335.562,1938.501"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_G2_19" d="M3296.938,2078.485L3286.816,2091.019C3276.694,2103.553,3256.45,2128.62,3246.327,2146.654C3236.205,2164.688,3236.205,2175.688,3236.205,2181.188L3236.205,2186.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_G3_20" d="M3374.343,2078.485L3384.298,2091.019C3394.254,2103.553,3414.165,2128.62,3424.121,2146.654C3434.076,2164.688,3434.076,2175.688,3434.076,2181.188L3434.076,2186.688"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F1_F2_51" d="M2976.647,2056.344L2958.786,2072.568C2940.924,2088.792,2905.2,2121.24,2899.925,2143.348C2894.65,2165.456,2919.823,2177.225,2932.41,2183.109L2944.996,2188.993"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_F1_52" d="M3059.064,2190.688L3071.098,2184.521C3083.132,2178.354,3107.201,2166.021,3103.412,2144.101C3099.622,2122.18,3067.975,2090.673,3052.151,2074.919L3036.328,2059.166"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3236.205078125, 2153.6875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(3434.076171875, 2153.6875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(0, 355.5)" class="root"><g class="clusters"><g data-look="classic" id="对象反序列化" class="cluster"><rect height="837" width="858" y="8" x="8" style=""></rect><g transform="translate(389, 8)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对象反序列化</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_J1_42" d="M301.301,99.5L301.301,105.75C301.301,112,301.301,124.5,301.375,136.417C301.45,148.333,301.599,159.667,301.674,165.334L301.748,171"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J1_J2_43" d="M261.828,269.027L239.69,283.856C217.552,298.685,173.276,328.342,151.138,350.755C129,373.167,129,388.333,129,395.917L129,403.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J1_J3_44" d="M335.755,275.046L349.962,288.872C364.17,302.698,392.585,330.349,406.792,351.758C421,373.167,421,388.333,421,395.917L421,403.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J3_J4_45" d="M421,461.5L421,467.75C421,474,421,486.5,421.075,498.417C421.149,510.333,421.298,521.667,421.373,527.334L421.447,533"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J4_J5_46" d="M377.301,610.801L330.584,626.334C283.867,641.867,190.434,672.934,143.717,696.05C97,719.167,97,734.333,97,741.917L97,749.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J4_J6_47" d="M385.821,619.321L364.017,633.434C342.214,647.547,298.607,675.774,276.803,697.47C255,719.167,255,734.333,255,741.917L255,749.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J4_J7_48" d="M421.5,655L421.417,663.167C421.333,671.333,421.167,687.667,421.083,703.417C421,719.167,421,734.333,421,741.917L421,749.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J4_J8_49" d="M457.84,618.66L480.7,632.883C503.56,647.107,549.28,675.553,572.14,697.36C595,719.167,595,734.333,595,741.917L595,749.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J4_J9_50" d="M466.477,610.023L516.897,625.686C567.318,641.349,668.159,672.674,718.579,695.921C769,719.167,769,734.333,769,741.917L769,749.5"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(129, 358)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>引用</p></span></div></foreignObject></g></g><g transform="translate(421, 358)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>内联</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(97, 704)" class="edgeLabel"><g transform="translate(-25.15625, -12)" class="label"><foreignObject height="24" width="50.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Library</p></span></div></foreignObject></g></g><g transform="translate(255, 704)" class="edgeLabel"><g transform="translate(-17.82421875, -12)" class="label"><foreignObject height="24" width="35.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Class</p></span></div></foreignObject></g></g><g transform="translate(421, 704)" class="edgeLabel"><g transform="translate(-28.609375, -12)" class="label"><foreignObject height="24" width="57.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Member</p></span></div></foreignObject></g></g><g transform="translate(595, 704)" class="edgeLabel"><g transform="translate(-16.48828125, -12)" class="label"><foreignObject height="24" width="32.9765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Type</p></span></div></foreignObject></g></g><g transform="translate(769, 704)" class="edgeLabel"><g transform="translate(-44.13671875, -12)" class="label"><foreignObject height="24" width="88.2734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>ConstObject</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(301.30078125, 72.5)" id="flowchart-J-195" class="node default"><rect height="54" width="143.265625" y="-27" x="-71.6328125" style="" class="basic label-container"></rect><g transform="translate(-41.6328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadObject</p></span></div></foreignObject></g></g><g transform="translate(301.30078125, 241.5)" id="flowchart-J1-196" class="node default"><polygon transform="translate(-67,67)" class="label-container" points="67,0 134,-67 67,-134 0,-67"></polygon><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>引用位检查</p></span></div></foreignObject></g></g><g transform="translate(129, 434.5)" id="flowchart-J2-198" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>从索引获取对象</p></span></div></foreignObject></g></g><g transform="translate(421, 434.5)" id="flowchart-J3-200" class="node default"><rect height="54" width="206.796875" y="-27" x="-103.3984375" style="" class="basic label-container"></rect><g transform="translate(-73.3984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadObjectContents</p></span></div></foreignObject></g></g><g transform="translate(421, 595.5)" id="flowchart-J4-202" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对象类型</p></span></div></foreignObject></g></g><g transform="translate(97, 780.5)" id="flowchart-J5-204" class="node default"><rect height="54" width="108" y="-27" x="-54" style="" class="basic label-container"></rect><g transform="translate(-24, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查找库</p></span></div></foreignObject></g></g><g transform="translate(255, 780.5)" id="flowchart-J6-206" class="node default"><rect height="54" width="108" y="-27" x="-54" style="" class="basic label-container"></rect><g transform="translate(-24, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查找类</p></span></div></foreignObject></g></g><g transform="translate(421, 780.5)" id="flowchart-J7-208" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查找成员</p></span></div></foreignObject></g></g><g transform="translate(595, 780.5)" id="flowchart-J8-210" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>构造类型</p></span></div></foreignObject></g></g><g transform="translate(769, 780.5)" id="flowchart-J9-212" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>构造常量</p></span></div></foreignObject></g></g></g></g><g transform="translate(908, 472)" class="root"><g class="clusters"><g data-look="classic" id="常量池处理" class="cluster"><rect height="604" width="1684" y="8" x="8" style=""></rect><g transform="translate(810, 8)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>常量池处理</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I1_32" d="M858,99.5L858,105.75C858,112,858,124.5,858,136.333C858,148.167,858,159.333,858,164.917L858,170.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I1_I2_33" d="M858,228.5L858,234.75C858,241,858,253.5,858.075,265.417C858.149,277.333,858.298,288.667,858.373,294.334L858.447,300"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I3_34" d="M807.071,370.571L692.726,387.309C578.381,404.048,349.69,437.524,235.345,461.845C121,486.167,121,501.333,121,508.917L121,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I4_35" d="M809.767,373.267L731.973,389.556C654.178,405.845,498.589,438.422,420.795,462.295C343,486.167,343,501.333,343,508.917L343,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I5_36" d="M815.132,378.632L772.11,394.027C729.088,409.422,643.044,440.211,600.022,463.189C557,486.167,557,501.333,557,508.917L557,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I6_37" d="M829.767,393.267L817.306,406.223C804.845,419.178,779.922,445.089,767.461,465.628C755,486.167,755,501.333,755,508.917L755,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I7_38" d="M887.233,393.267L899.527,406.223C911.822,419.178,936.411,445.089,948.705,465.628C961,486.167,961,501.333,961,508.917L961,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I8_39" d="M902.167,378.333L946.306,393.777C990.445,409.222,1078.722,440.111,1122.861,463.139C1167,486.167,1167,501.333,1167,508.917L1167,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I9_40" d="M907.233,373.267L984.861,389.556C1062.489,405.845,1217.744,438.422,1295.372,462.295C1373,486.167,1373,501.333,1373,508.917L1373,516.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I10_41" d="M909.783,370.717L1021.319,387.431C1132.855,404.145,1355.928,437.572,1467.464,461.87C1579,486.167,1579,501.333,1579,508.917L1579,516.5"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(121, 471)" class="edgeLabel"><g transform="translate(-38.296875, -12)" class="label"><foreignObject height="24" width="76.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>StaticField</p></span></div></foreignObject></g></g><g transform="translate(343, 471)" class="edgeLabel"><g transform="translate(-47.56640625, -12)" class="label"><foreignObject height="24" width="95.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>InstanceField</p></span></div></foreignObject></g></g><g transform="translate(557, 471)" class="edgeLabel"><g transform="translate(-17.82421875, -12)" class="label"><foreignObject height="24" width="35.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Class</p></span></div></foreignObject></g></g><g transform="translate(755, 471)" class="edgeLabel"><g transform="translate(-16.48828125, -12)" class="label"><foreignObject height="24" width="32.9765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Type</p></span></div></foreignObject></g></g><g transform="translate(961, 471)" class="edgeLabel"><g transform="translate(-35.5, -12)" class="label"><foreignObject height="24" width="71"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>DirectCall</p></span></div></foreignObject></g></g><g transform="translate(1167, 471)" class="edgeLabel"><g transform="translate(-46.43359375, -12)" class="label"><foreignObject height="24" width="92.8671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>InterfaceCall</p></span></div></foreignObject></g></g><g transform="translate(1373, 471)" class="edgeLabel"><g transform="translate(-44.015625, -12)" class="label"><foreignObject height="24" width="88.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>DynamicCall</p></span></div></foreignObject></g></g><g transform="translate(1579, 471)" class="edgeLabel"><g transform="translate(-35.9296875, -12)" class="label"><foreignObject height="24" width="71.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>ObjectRef</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(858, 72.5)" id="flowchart-I-175" class="node default"><rect height="54" width="187.984375" y="-27" x="-93.9921875" style="" class="basic label-container"></rect><g transform="translate(-63.9921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadConstantPool</p></span></div></foreignObject></g></g><g transform="translate(858, 201.5)" id="flowchart-I1-176" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>遍历池条目</p></span></div></foreignObject></g></g><g transform="translate(858, 362.5)" id="flowchart-I2-178" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>条目类型</p></span></div></foreignObject></g></g><g transform="translate(121, 547.5)" id="flowchart-I3-180" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取静态字段</p></span></div></foreignObject></g></g><g transform="translate(343, 547.5)" id="flowchart-I4-182" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取实例字段偏移</p></span></div></foreignObject></g></g><g transform="translate(557, 547.5)" id="flowchart-I5-184" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取类引用</p></span></div></foreignObject></g></g><g transform="translate(755, 547.5)" id="flowchart-I6-186" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取类型信息</p></span></div></foreignObject></g></g><g transform="translate(961, 547.5)" id="flowchart-I7-188" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取直接调用</p></span></div></foreignObject></g></g><g transform="translate(1167, 547.5)" id="flowchart-I8-190" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取接口调用</p></span></div></foreignObject></g></g><g transform="translate(1373, 547.5)" id="flowchart-I9-192" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取动态调用</p></span></div></foreignObject></g></g><g transform="translate(1579, 547.5)" id="flowchart-I10-194" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取对象引用</p></span></div></foreignObject></g></g></g></g><g transform="translate(2642, 0)" class="root"><g class="clusters"><g data-look="classic" id="subGraph0" class="cluster"><rect height="1548" width="258" y="8" x="8" style=""></rect><g transform="translate(67.3359375, 8)" class="cluster-label"><foreignObject height="24" width="139.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadCode 详细流程</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_H1_21" d="M137,99.5L137,105.75C137,112,137,124.5,137,136.333C137,148.167,137,159.333,137,164.917L137,170.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H1_H2_22" d="M137,228.5L137,234.75C137,241,137,253.5,137,265.333C137,277.167,137,288.333,137,293.917L137,299.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H2_H3_23" d="M137,357.5L137,363.75C137,370,137,382.5,137,394.333C137,406.167,137,417.333,137,422.917L137,428.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H3_H4_24" d="M137,486.5L137,492.75C137,499,137,511.5,137,523.333C137,535.167,137,546.333,137,551.917L137,557.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H4_H5_25" d="M137,615.5L137,621.75C137,628,137,640.5,137,652.333C137,664.167,137,675.333,137,680.917L137,686.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H5_H6_26" d="M137,744.5L137,750.75C137,757,137,769.5,137,781.333C137,793.167,137,804.333,137,809.917L137,815.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H6_H7_27" d="M137,873.5L137,879.75C137,886,137,898.5,137,910.333C137,922.167,137,933.333,137,938.917L137,944.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H7_H8_28" d="M137,1002.5L137,1008.75C137,1015,137,1027.5,137,1039.333C137,1051.167,137,1062.333,137,1067.917L137,1073.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H8_H9_29" d="M137,1131.5L137,1137.75C137,1144,137,1156.5,137,1168.333C137,1180.167,137,1191.333,137,1196.917L137,1202.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H9_H10_30" d="M137,1260.5L137,1266.75C137,1273,137,1285.5,137,1297.333C137,1309.167,137,1320.333,137,1325.917L137,1331.5"></path><path marker-end="url(#mermaid-024debbb-71c6-43f2-b3b5-7a00442ff689_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H10_H11_31" d="M137,1389.5L137,1395.75C137,1402,137,1414.5,137,1426.333C137,1438.167,137,1449.333,137,1454.917L137,1460.5"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(137, 72.5)" id="flowchart-H-153" class="node default"><rect height="54" width="167.328125" y="-27" x="-83.6640625" style="" class="basic label-container"></rect><g transform="translate(-53.6640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="107.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadCode 开始</p></span></div></foreignObject></g></g><g transform="translate(137, 201.5)" id="flowchart-H1-154" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取代码标志</p></span></div></foreignObject></g></g><g transform="translate(137, 330.5)" id="flowchart-H2-156" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解析各种标志位</p></span></div></foreignObject></g></g><g transform="translate(137, 459.5)" id="flowchart-H3-158" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取参数标志</p></span></div></foreignObject></g></g><g transform="translate(137, 588.5)" id="flowchart-H4-160" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取闭包声明</p></span></div></foreignObject></g></g><g transform="translate(137, 717.5)" id="flowchart-H5-162" class="node default"><rect height="54" width="187.984375" y="-27" x="-93.9921875" style="" class="basic label-container"></rect><g transform="translate(-63.9921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadConstantPool</p></span></div></foreignObject></g></g><g transform="translate(137, 846.5)" id="flowchart-H6-164" class="node default"><rect height="54" width="160.875" y="-27" x="-80.4375" style="" class="basic label-container"></rect><g transform="translate(-50.4375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadBytecode</p></span></div></foreignObject></g></g><g transform="translate(137, 975.5)" id="flowchart-H7-166" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取异常表</p></span></div></foreignObject></g></g><g transform="translate(137, 1104.5)" id="flowchart-H8-168" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取源位置信息</p></span></div></foreignObject></g></g><g transform="translate(137, 1233.5)" id="flowchart-H9-170" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取局部变量信息</p></span></div></foreignObject></g></g><g transform="translate(137, 1362.5)" id="flowchart-H10-172" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>处理可空字段</p></span></div></foreignObject></g></g><g transform="translate(137, 1491.5)" id="flowchart-H11-174" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取闭包代码</p></span></div></foreignObject></g></g></g></g><g transform="translate(3049.34765625, 782)" id="flowchart-A-111" class="node default"><rect height="54" width="182.6953125" y="-27" x="-91.34765625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-61.34765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开始: 二进制数据</p></span></div></foreignObject></g></g><g transform="translate(3049.34765625, 1633)" id="flowchart-B-112" class="node default"><rect height="54" width="211.9375" y="-27" x="-105.96875" style="" class="basic label-container"></rect><g transform="translate(-75.96875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BytecodeLoader 构造</p></span></div></foreignObject></g></g><g transform="translate(3049.34765625, 1737)" id="flowchart-C-114" class="node default"><rect height="54" width="212.9921875" y="-27" x="-106.49609375" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-76.49609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LoadBytecode 主入口</p></span></div></foreignObject></g></g><g transform="translate(2418.787109375, 1853)" id="flowchart-D-116" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Step 1: ReadBytecodeComponent</p></span></div></foreignObject></g></g><g transform="translate(2418.787109375, 2029.34375)" id="flowchart-D1-118" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>验证魔数和版本</p></span></div></foreignObject></g></g><g transform="translate(2418.787109375, 2217.6875)" id="flowchart-D2-120" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解析文件头结构</p></span></div></foreignObject></g></g><g transform="translate(2418.787109375, 2321.6875)" id="flowchart-D3-122" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取各段偏移信息</p></span></div></foreignObject></g></g><g transform="translate(2418.787109375, 2437.6875)" id="flowchart-D4-124" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建 BytecodeComponentData</p></span></div></foreignObject></g></g><g transform="translate(2712.580078125, 1853)" id="flowchart-E-126" class="node default"><rect height="54" width="227.5859375" y="-27" x="-113.79296875" style="" class="basic label-container"></rect><g transform="translate(-83.79296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.5859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Step 2: 初始化组件数据</p></span></div></foreignObject></g></g><g transform="translate(2712.580078125, 2029.34375)" id="flowchart-E1-128" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建 BytecodeReaderHelper</p></span></div></foreignObject></g></g><g transform="translate(2712.580078125, 2217.6875)" id="flowchart-E2-130" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置读取器状态</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 1853)" id="flowchart-F-132" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Step 3: ReadLibraryDeclarations</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2029.34375)" id="flowchart-F1-134" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取库索引</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2217.6875)" id="flowchart-F2-135" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>处理每个库</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2321.6875)" id="flowchart-F3-136" class="node default"><rect height="54" width="227.46875" y="-27" x="-113.734375" style="" class="basic label-container"></rect><g transform="translate(-83.734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadLibraryDeclaration</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2437.6875)" id="flowchart-F4-138" class="node default"><rect height="54" width="212.8046875" y="-27" x="-106.40234375" style="" class="basic label-container"></rect><g transform="translate(-76.40234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.8046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadClassDeclaration</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2553.6875)" id="flowchart-F5-140" class="node default"><rect height="54" width="158.40625" y="-27" x="-79.203125" style="" class="basic label-container"></rect><g transform="translate(-49.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadMembers</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2657.6875)" id="flowchart-F6-142" class="node default"><rect height="54" width="218.953125" y="-27" x="-109.4765625" style="" class="basic label-container"></rect><g transform="translate(-79.4765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="158.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadFieldDeclarations</p></span></div></foreignObject></g></g><g transform="translate(3006.373046875, 2761.6875)" id="flowchart-F7-144" class="node default"><rect height="54" width="245.671875" y="-27" x="-122.8359375" style="" class="basic label-container"></rect><g transform="translate(-92.8359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReadFunctionDeclarations</p></span></div></foreignObject></g></g><g transform="translate(3335.140625, 1853)" id="flowchart-G-146" class="node default"><rect height="54" width="195.5859375" y="-27" x="-97.79296875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-67.79296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="135.5859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Step 4: 处理主函数</p></span></div></foreignObject></g></g><g transform="translate(3335.140625, 2029.34375)" id="flowchart-G1-148" class="node default"><polygon transform="translate(-87.34375,87.34375)" class="label-container" points="87.34375,0 174.6875,-87.34375 87.34375,-174.6875 0,-87.34375"></polygon><g transform="translate(-60.34375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="120.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>主函数偏移 == 0?</p></span></div></foreignObject></g></g><g transform="translate(3236.205078125, 2217.6875)" id="flowchart-G2-150" class="node default"><rect height="54" width="123.7421875" y="-27" x="-61.87109375" style="" class="basic label-container"></rect><g transform="translate(-31.87109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="63.7421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回 null</p></span></div></foreignObject></g></g><g transform="translate(3434.076171875, 2217.6875)" id="flowchart-G3-152" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>读取主函数对象</p></span></div></foreignObject></g></g></g></g></g></svg>