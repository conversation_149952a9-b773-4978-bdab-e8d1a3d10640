// AOT + Bytecode 混合加载示例
// 这个文件展示了如何在 AOT 运行时上安全地加载 bytecode 数据

#include "vm/bytecode_reader.h"
#include "vm/dart_api_impl.h"
#include "vm/isolate.h"
#include "vm/object.h"

namespace dart {

class HybridBytecodeLoader {
 public:
  // 安全的混合加载入口点
  static Dart_Handle LoadBytecodeOnAOT(const uint8_t* bytecode_data, 
                                       intptr_t bytecode_size) {
    Thread* thread = Thread::Current();
    ASSERT(thread != nullptr);
    
    // 检查当前是否为 AOT 模式
    if (!FLAG_precompiled_mode) {
      return Api::NewError("Hybrid loading only supported in AOT mode");
    }
    
    std::cout << "[HYBRID] Starting AOT + Bytecode hybrid loading" << std::endl;
    std::cout << "[HYBRID] Bytecode size: " << bytecode_size << " bytes" << std::endl;
    
    // 创建外部类型化数据
    const auto& typed_data = ExternalTypedData::Handle(
        thread->zone(),
        ExternalTypedData::NewFinalizeWithFree(
            const_cast<uint8_t*>(bytecode_data), bytecode_size));
    
    // 使用增量加载器
    {
      SafepointWriteRwLocker ml(thread, thread->isolate_group()->program_lock());
      
      bytecode::BytecodeLoader loader(thread, typed_data);
      FunctionPtr main_function = loader.LoadBytecodeIncremental();
      
      if (main_function != Function::null()) {
        std::cout << "[HYBRID] Successfully loaded main function" << std::endl;
        return Api::NewHandle(thread, main_function);
      } else {
        std::cout << "[HYBRID] No main function found in bytecode" << std::endl;
        return Api::Null();
      }
    }
  }
  
  // 检查库兼容性
  static bool CheckLibraryCompatibility(const String& uri) {
    Thread* thread = Thread::Current();
    LibraryPtr existing_lib = Library::LookupLibrary(thread, uri);
    
    if (existing_lib == Library::null()) {
      std::cout << "[HYBRID] Library " << uri.ToCString() << " is new" << std::endl;
      return true;
    }
    
    std::cout << "[HYBRID] Library " << uri.ToCString() << " already exists" << std::endl;
    
    // 检查是否为核心库（通常不应该被覆盖）
    if (uri.StartsWith(Symbols::DartScheme())) {
      std::cout << "[HYBRID] Core library - will be merged incrementally" << std::endl;
      return true;
    }
    
    return true;
  }
  
  // 获取混合运行时统计信息
  static void PrintHybridStats() {
    Thread* thread = Thread::Current();
    IsolateGroup* ig = thread->isolate_group();
    
    const auto& libraries = GrowableObjectArray::Handle(
        thread->zone(), ig->object_store()->libraries());
    
    intptr_t aot_libraries = 0;
    intptr_t bytecode_libraries = 0;
    intptr_t hybrid_libraries = 0;
    
    Library& lib = Library::Handle();
    for (intptr_t i = 0; i < libraries.Length(); i++) {
      lib ^= libraries.At(i);
      
      bool has_aot_code = false;
      bool has_bytecode = false;
      
      // 检查库中的类
      const auto& classes = GrowableObjectArray::Handle(lib.classes());
      Class& cls = Class::Handle();
      
      for (intptr_t j = 0; j < classes.Length(); j++) {
        cls ^= classes.At(j);
        
        // 检查类中的函数
        const auto& functions = Array::Handle(cls.functions());
        Function& func = Function::Handle();
        
        for (intptr_t k = 0; k < functions.Length(); k++) {
          func ^= functions.At(k);
          
          if (func.HasCode()) {
            has_aot_code = true;
          }
          
#if defined(DART_DYNAMIC_MODULES)
          if (func.HasBytecode()) {
            has_bytecode = true;
          }
#endif
        }
      }
      
      if (has_aot_code && has_bytecode) {
        hybrid_libraries++;
      } else if (has_aot_code) {
        aot_libraries++;
      } else if (has_bytecode) {
        bytecode_libraries++;
      }
    }
    
    std::cout << "[HYBRID] Runtime Statistics:" << std::endl;
    std::cout << "[HYBRID] - AOT-only libraries: " << aot_libraries << std::endl;
    std::cout << "[HYBRID] - Bytecode-only libraries: " << bytecode_libraries << std::endl;
    std::cout << "[HYBRID] - Hybrid libraries: " << hybrid_libraries << std::endl;
    std::cout << "[HYBRID] - Total libraries: " << libraries.Length() << std::endl;
  }
};

// C API 包装器
extern "C" {

// 主要的混合加载 API
DART_EXPORT Dart_Handle Dart_LoadBytecodeOnAOT(const uint8_t* bytecode_data,
                                               intptr_t bytecode_size) {
  DARTSCOPE(Thread::Current());
  return HybridBytecodeLoader::LoadBytecodeOnAOT(bytecode_data, bytecode_size);
}

// 检查兼容性 API
DART_EXPORT bool Dart_CheckBytecodeCompatibility(const char* library_uri) {
  DARTSCOPE(Thread::Current());
  const String& uri = String::Handle(String::New(library_uri));
  return HybridBytecodeLoader::CheckLibraryCompatibility(uri);
}

// 获取统计信息 API
DART_EXPORT void Dart_PrintHybridRuntimeStats() {
  DARTSCOPE(Thread::Current());
  HybridBytecodeLoader::PrintHybridStats();
}

}  // extern "C"

}  // namespace dart

// 使用示例
/*

// 在 C++ 代码中使用
void LoadAdditionalBytecode() {
  // 读取 bytecode 文件
  std::ifstream file("additional_code.dill", std::ios::binary);
  std::vector<uint8_t> bytecode_data(
      (std::istreambuf_iterator<char>(file)),
      std::istreambuf_iterator<char>());
  
  // 加载到 AOT 运行时
  Dart_Handle result = Dart_LoadBytecodeOnAOT(
      bytecode_data.data(), bytecode_data.size());
  
  if (Dart_IsError(result)) {
    std::cerr << "Failed to load bytecode: " << Dart_GetError(result) << std::endl;
  } else {
    std::cout << "Successfully loaded bytecode" << std::endl;
    Dart_PrintHybridRuntimeStats();
  }
}

// 在 Dart 代码中使用（通过 FFI）
import 'dart:ffi';
import 'dart:io';

typedef LoadBytecodeNative = Handle Function(Pointer<Uint8>, IntPtr);
typedef LoadBytecode = Object Function(Pointer<Uint8>, int);

void loadAdditionalCode() {
  final dylib = DynamicLibrary.process();
  final loadBytecode = dylib
      .lookup<NativeFunction<LoadBytecodeNative>>('Dart_LoadBytecodeOnAOT')
      .asFunction<LoadBytecode>();
  
  final file = File('additional_code.dill');
  final bytes = file.readAsBytesSync();
  
  final pointer = malloc<Uint8>(bytes.length);
  pointer.asTypedList(bytes.length).setAll(0, bytes);
  
  try {
    final result = loadBytecode(pointer, bytes.length);
    print('Bytecode loaded successfully: $result');
  } finally {
    malloc.free(pointer);
  }
}

*/
