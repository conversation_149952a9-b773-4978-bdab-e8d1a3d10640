// Copyright (c) 2020, the Dart project authors. Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

/// Map of error code unique name to custom correction.
const Map<String, String> customizedCorrections = {};

/// Map of error code unique name to custom message.
const Map<String, String> customizedMessages = {};
